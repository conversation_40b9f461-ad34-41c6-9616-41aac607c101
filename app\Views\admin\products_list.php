<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .product-image {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 8px;
    }
    
    .status-badge {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
    
    .action-buttons {
        display: flex;
        gap: 0.25rem;
    }
    
    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
        border-radius: 4px;
    }
    
    .filters-card {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
    }
    
    .table-actions {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .bulk-actions {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .table-info {
        color: var(--admin-muted);
        font-size: 0.9rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Filters -->
<div class="filters-card">
    <div class="row g-3">
        <div class="col-md-3">
            <label class="form-label">Search Products</label>
            <input type="text" class="form-control" placeholder="Search by name, farmer..." id="searchInput">
        </div>
        <div class="col-md-2">
            <label class="form-label">Category</label>
            <select class="form-select" id="categoryFilter">
                <option value="">All Categories</option>
                <option value="grains">Grains</option>
                <option value="vegetables">Vegetables</option>
                <option value="fruits">Fruits</option>
                <option value="pulses">Pulses</option>
            </select>
        </div>
        <div class="col-md-2">
            <label class="form-label">Status</label>
            <select class="form-select" id="statusFilter">
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="pending">Pending</option>
                <option value="out-of-stock">Out of Stock</option>
            </select>
        </div>
        <div class="col-md-2">
            <label class="form-label">Sort By</label>
            <select class="form-select" id="sortBy">
                <option value="name">Name</option>
                <option value="price">Price</option>
                <option value="stock">Stock</option>
                <option value="date">Date Added</option>
            </select>
        </div>
        <div class="col-md-3 d-flex align-items-end gap-2">
            <button type="button" class="btn btn-admin-primary" onclick="applyFilters()">
                <i class="fas fa-filter me-2"></i>Apply Filters
            </button>
            <button type="button" class="btn btn-admin-secondary" onclick="clearFilters()">
                <i class="fas fa-times me-2"></i>Clear
            </button>
        </div>
    </div>
</div>

<!-- Table Actions -->
<div class="table-actions">
    <div class="bulk-actions">
        <input type="checkbox" class="form-check-input" id="selectAll">
        <label class="form-check-label ms-2" for="selectAll">Select All</label>
        <button type="button" class="btn btn-sm btn-outline-danger ms-3" onclick="bulkDelete()" disabled id="bulkDeleteBtn">
            <i class="fas fa-trash me-1"></i>Delete Selected
        </button>
        <button type="button" class="btn btn-sm btn-outline-success ms-2" onclick="bulkActivate()" disabled id="bulkActivateBtn">
            <i class="fas fa-check me-1"></i>Activate Selected
        </button>
    </div>
    <div class="table-info">
        Showing <span id="showingStart">1</span>-<span id="showingEnd">25</span> of <span id="totalRecords">247</span> products
    </div>
</div>

<!-- Products Table -->
<div class="admin-table">
    <table class="table table-hover" id="productsTable">
        <thead>
            <tr>
                <th width="40">
                    <input type="checkbox" class="form-check-input" id="selectAllHeader">
                </th>
                <th width="80">Image</th>
                <th data-sortable>Product Name</th>
                <th data-sortable>Category</th>
                <th data-sortable>Farmer</th>
                <th data-sortable>Price</th>
                <th data-sortable>Stock</th>
                <th>Status</th>
                <th>Date Added</th>
                <th width="120">Actions</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td><input type="checkbox" class="form-check-input row-select" value="1"></td>
                <td>
                    <img src="<?= base_url('public/assets/images/products/rice.jpg') ?>" 
                         alt="Premium Rice" class="product-image"
                         onerror="this.src='<?= base_url('public/assets/images/default-product.png') ?>'">
                </td>
                <td>
                    <div class="fw-bold">Premium Basmati Rice</div>
                    <small class="text-muted">SKU: PRD-001</small>
                </td>
                <td><span class="badge bg-info">Grains</span></td>
                <td>
                    <div>John Farmer</div>
                    <small class="text-muted"><EMAIL></small>
                </td>
                <td>
                    <div class="fw-bold text-success">$4.50/kg</div>
                    <small class="text-muted">Min: 10kg</small>
                </td>
                <td>
                    <div class="fw-bold">500kg</div>
                    <small class="text-success">In Stock</small>
                </td>
                <td><span class="badge bg-success status-badge">Active</span></td>
                <td>
                    <div>Jan 15, 2024</div>
                    <small class="text-muted">2:30 PM</small>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline-primary btn-action" onclick="viewProduct(1)" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary btn-action" onclick="editProduct(1)" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger btn-action" onclick="deleteProduct(1)" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
            
            <tr>
                <td><input type="checkbox" class="form-check-input row-select" value="2"></td>
                <td>
                    <img src="<?= base_url('public/assets/images/products/wheat.jpg') ?>" 
                         alt="Organic Wheat" class="product-image"
                         onerror="this.src='<?= base_url('public/assets/images/default-product.png') ?>'">
                </td>
                <td>
                    <div class="fw-bold">Organic Wheat</div>
                    <small class="text-muted">SKU: PRD-002</small>
                </td>
                <td><span class="badge bg-info">Grains</span></td>
                <td>
                    <div>Mike Smith</div>
                    <small class="text-muted"><EMAIL></small>
                </td>
                <td>
                    <div class="fw-bold text-success">$3.20/kg</div>
                    <small class="text-muted">Min: 25kg</small>
                </td>
                <td>
                    <div class="fw-bold text-warning">50kg</div>
                    <small class="text-warning">Low Stock</small>
                </td>
                <td><span class="badge bg-warning status-badge">Low Stock</span></td>
                <td>
                    <div>Jan 14, 2024</div>
                    <small class="text-muted">10:15 AM</small>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline-primary btn-action" onclick="viewProduct(2)" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary btn-action" onclick="editProduct(2)" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger btn-action" onclick="deleteProduct(2)" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
            
            <tr>
                <td><input type="checkbox" class="form-check-input row-select" value="3"></td>
                <td>
                    <img src="<?= base_url('public/assets/images/products/tomatoes.jpg') ?>" 
                         alt="Fresh Tomatoes" class="product-image"
                         onerror="this.src='<?= base_url('public/assets/images/default-product.png') ?>'">
                </td>
                <td>
                    <div class="fw-bold">Fresh Tomatoes</div>
                    <small class="text-muted">SKU: PRD-003</small>
                </td>
                <td><span class="badge bg-success">Vegetables</span></td>
                <td>
                    <div>Sarah Johnson</div>
                    <small class="text-muted"><EMAIL></small>
                </td>
                <td>
                    <div class="fw-bold text-success">$2.80/kg</div>
                    <small class="text-muted">Min: 5kg</small>
                </td>
                <td>
                    <div class="fw-bold">200kg</div>
                    <small class="text-success">In Stock</small>
                </td>
                <td><span class="badge bg-success status-badge">Active</span></td>
                <td>
                    <div>Jan 16, 2024</div>
                    <small class="text-muted">8:45 AM</small>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline-primary btn-action" onclick="viewProduct(3)" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary btn-action" onclick="editProduct(3)" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger btn-action" onclick="deleteProduct(3)" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
            
            <tr>
                <td><input type="checkbox" class="form-check-input row-select" value="4"></td>
                <td>
                    <img src="<?= base_url('public/assets/images/products/corn.jpg') ?>" 
                         alt="Sweet Corn" class="product-image"
                         onerror="this.src='<?= base_url('public/assets/images/default-product.png') ?>'">
                </td>
                <td>
                    <div class="fw-bold">Sweet Corn</div>
                    <small class="text-muted">SKU: PRD-004</small>
                </td>
                <td><span class="badge bg-success">Vegetables</span></td>
                <td>
                    <div>David Brown</div>
                    <small class="text-muted"><EMAIL></small>
                </td>
                <td>
                    <div class="fw-bold text-success">$3.50/kg</div>
                    <small class="text-muted">Min: 10kg</small>
                </td>
                <td>
                    <div class="fw-bold text-danger">0kg</div>
                    <small class="text-danger">Out of Stock</small>
                </td>
                <td><span class="badge bg-danger status-badge">Out of Stock</span></td>
                <td>
                    <div>Jan 12, 2024</div>
                    <small class="text-muted">3:20 PM</small>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline-primary btn-action" onclick="viewProduct(4)" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary btn-action" onclick="editProduct(4)" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger btn-action" onclick="deleteProduct(4)" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Pagination -->
<div class="d-flex justify-content-between align-items-center mt-3">
    <div>
        <select class="form-select form-select-sm" style="width: auto;" onchange="changePageSize(this.value)">
            <option value="25">25 per page</option>
            <option value="50">50 per page</option>
            <option value="100">100 per page</option>
        </select>
    </div>
    
    <nav aria-label="Products pagination">
        <ul class="pagination pagination-sm mb-0">
            <li class="page-item disabled">
                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
            </li>
            <li class="page-item active"><a class="page-link" href="#">1</a></li>
            <li class="page-item"><a class="page-link" href="#">2</a></li>
            <li class="page-item"><a class="page-link" href="#">3</a></li>
            <li class="page-item">
                <a class="page-link" href="#">Next</a>
            </li>
        </ul>
    </nav>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize data table functionality
    AdminTemplate.initializeDataTable('productsTable');
    
    // Handle select all functionality
    const selectAll = document.getElementById('selectAll');
    const selectAllHeader = document.getElementById('selectAllHeader');
    const rowSelects = document.querySelectorAll('.row-select');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const bulkActivateBtn = document.getElementById('bulkActivateBtn');
    
    function updateBulkButtons() {
        const checkedBoxes = document.querySelectorAll('.row-select:checked');
        const hasSelection = checkedBoxes.length > 0;
        
        bulkDeleteBtn.disabled = !hasSelection;
        bulkActivateBtn.disabled = !hasSelection;
    }
    
    // Select all functionality
    [selectAll, selectAllHeader].forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            rowSelects.forEach(select => {
                select.checked = this.checked;
            });
            
            // Sync both select all checkboxes
            if (this === selectAll) {
                selectAllHeader.checked = this.checked;
            } else {
                selectAll.checked = this.checked;
            }
            
            updateBulkButtons();
        });
    });
    
    // Individual row select
    rowSelects.forEach(select => {
        select.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.row-select:checked').length;
            const totalCount = rowSelects.length;
            
            selectAll.checked = checkedCount === totalCount;
            selectAllHeader.checked = checkedCount === totalCount;
            selectAll.indeterminate = checkedCount > 0 && checkedCount < totalCount;
            selectAllHeader.indeterminate = checkedCount > 0 && checkedCount < totalCount;
            
            updateBulkButtons();
        });
    });
});

function applyFilters() {
    const search = document.getElementById('searchInput').value;
    const category = document.getElementById('categoryFilter').value;
    const status = document.getElementById('statusFilter').value;
    const sortBy = document.getElementById('sortBy').value;
    
    // Implement filtering logic here
    AdminTemplate.showNotification('Filters applied successfully!', 'success');
}

function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('categoryFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('sortBy').value = 'name';
    
    AdminTemplate.showNotification('Filters cleared!', 'info');
}

function viewProduct(id) {
    window.location.href = `<?= base_url('admin/products/view/') ?>${id}`;
}

function editProduct(id) {
    window.location.href = `<?= base_url('admin/products/edit/') ?>${id}`;
}

function deleteProduct(id) {
    AdminTemplate.confirmAction('Are you sure you want to delete this product?', function() {
        // Implement delete logic here
        AdminTemplate.showNotification('Product deleted successfully!', 'success');
        // Refresh table or remove row
    });
}

function bulkDelete() {
    const selected = document.querySelectorAll('.row-select:checked');
    AdminTemplate.confirmAction(`Are you sure you want to delete ${selected.length} selected products?`, function() {
        // Implement bulk delete logic here
        AdminTemplate.showNotification(`${selected.length} products deleted successfully!`, 'success');
        // Refresh table
    });
}

function bulkActivate() {
    const selected = document.querySelectorAll('.row-select:checked');
    AdminTemplate.confirmAction(`Are you sure you want to activate ${selected.length} selected products?`, function() {
        // Implement bulk activate logic here
        AdminTemplate.showNotification(`${selected.length} products activated successfully!`, 'success');
        // Refresh table
    });
}

function changePageSize(size) {
    // Implement page size change logic here
    AdminTemplate.showNotification(`Page size changed to ${size} items`, 'info');
}
</script>
<?= $this->endSection() ?>