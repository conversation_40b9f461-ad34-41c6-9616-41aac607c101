<?php

namespace App\Controllers;

use App\Models\MissionModel;
use App\Models\UserModel;
use App\Models\CommodityModel;

class MissionController extends BaseController
{
    protected $missionModel;
    protected $userModel;
    protected $commodityModel;
    protected $data = [];

    public function __construct()
    {
        $this->missionModel = new MissionModel();
        $this->userModel = new UserModel();
        $this->commodityModel = new CommodityModel();
        
        // Initialize common admin data
        $this->data['user_name'] = session('fullname') ?? session('username') ?? 'User';
        $this->data['user_email'] = session('email') ?? '';
        $this->data['user_role'] = session('is_admin') ? 'administrator' : (session('is_supervisor') ? 'supervisor' : 'user');
    }

    /**
     * Display missions list
     */
    public function index()
    {
        $this->data['title'] = 'Mission Management - DCBuyer';
        $this->data['description'] = 'Manage missions and track their progress';
        $this->data['active_menu'] = 'missions';
        
        // Breadcrumbs
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Missions', 'url' => base_url('admin/missions')]
        ];
        
        // Page header
        $this->data['page_title'] = 'Mission Management';
        $this->data['page_description'] = 'View and manage all missions in the system.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/missions/create') . '" class="btn btn-admin-primary">
                <i class="fas fa-plus me-2"></i>Add New Mission
            </a>
        ';

        // Get search and filter parameters
        $search = $this->request->getGet('search') ?? '';
        $status = $this->request->getGet('status') ?? '';
        $perPage = 20;

        // Get missions with pagination
        $missions = $this->missionModel->getMissionsPaginated($perPage, $search, $status);
        $pager = $this->missionModel->pager;

        // Get mission statistics
        $stats = $this->missionModel->getMissionStats();

        $this->data['missions'] = $missions;
        $this->data['pager'] = $pager;
        $this->data['search'] = $search;
        $this->data['status_filter'] = $status;
        $this->data['stats'] = $stats;

        return view('admin/missions/mission_index', $this->data);
    }

    /**
     * Show mission details
     */
    public function show($id = null)
    {
        if (!$id) {
            return redirect()->to('admin/missions')->with('error', 'Mission ID is required.');
        }

        $mission = $this->missionModel->getMissionWithDetails($id);
        
        if (!$mission) {
            return redirect()->to('admin/missions')->with('error', 'Mission not found.');
        }

        $this->data['title'] = 'Mission Details - ' . $mission['mission_name'];
        $this->data['description'] = 'View mission details and information';
        $this->data['active_menu'] = 'missions';
        
        // Breadcrumbs
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Missions', 'url' => base_url('admin/missions')],
            ['title' => $mission['mission_name'], 'url' => '']
        ];
        
        // Page header
        $this->data['page_title'] = 'Mission Details';
        $this->data['page_description'] = 'View detailed information about this mission.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/missions/' . $mission['id'] . '/edit') . '" class="btn btn-admin-primary me-2">
                <i class="fas fa-edit me-2"></i>Edit Mission
            </a>
            <a href="' . base_url('admin/missions') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to List
            </a>
        ';

        $this->data['mission'] = $mission;

        return view('admin/missions/mission_show', $this->data);
    }

    /**
     * Show create mission form
     */
    public function create()
    {
        $this->data['title'] = 'Create New Mission - DCBuyer';
        $this->data['description'] = 'Create a new mission in the system';
        $this->data['active_menu'] = 'missions';
        
        // Breadcrumbs
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Missions', 'url' => base_url('admin/missions')],
            ['title' => 'Create Mission', 'url' => '']
        ];
        
        // Page header
        $this->data['page_title'] = 'Create New Mission';
        $this->data['page_description'] = 'Add a new mission to the system.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/missions') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to List
            </a>
        ';

        // Get buyers (users with is_buyer = true)
        $buyers = $this->userModel->where('is_buyer', true)
                                 ->where('status', 'active')
                                 ->orderBy('fullname', 'ASC')
                                 ->findAll();

        // Get active commodities
        $commodities = $this->commodityModel->getActiveCommodities();

        // Initialize form data
        $this->data['mission'] = [
            'mission_name' => old('mission_name'),
            'mission_date' => old('mission_date'),
            'mission_status' => old('mission_status', 'pending'),
            'user_id' => old('user_id'),
            'commodity_id' => old('commodity_id'),
            'budgeted_amount' => old('budgeted_amount'),
            'actual_amount' => old('actual_amount'),
            'remarks' => old('remarks')
        ];

        $this->data['buyers'] = $buyers;
        $this->data['commodities'] = $commodities;
        $this->data['validation'] = \Config\Services::validation();

        return view('admin/missions/mission_create', $this->data);
    }

    /**
     * Store new mission
     */
    public function store()
    {
        $rules = [
            'mission_name' => 'required|min_length[3]|max_length[255]',
            'mission_date' => 'required|valid_date',
            'mission_status' => 'required|in_list[pending,in_progress,completed,cancelled]',
            'user_id' => 'permit_empty|integer',
            'commodity_id' => 'permit_empty|integer',
            'budgeted_amount' => 'permit_empty|decimal',
            'actual_amount' => 'permit_empty|decimal',
            'remarks' => 'permit_empty|max_length[1000]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('validation', $this->validator);
        }

        $data = [
            'mission_name' => $this->request->getPost('mission_name'),
            'mission_date' => $this->request->getPost('mission_date'),
            'mission_status' => $this->request->getPost('mission_status'),
            'user_id' => $this->request->getPost('user_id') ?: null,
            'commodity_id' => $this->request->getPost('commodity_id') ?: null,
            'budgeted_amount' => $this->request->getPost('budgeted_amount') ?: null,
            'actual_amount' => $this->request->getPost('actual_amount') ?: null,
            'remarks' => $this->request->getPost('remarks')
            // mission_number will be auto-generated by the model
        ];

        // Check if mission name already exists
        if ($this->missionModel->missionNameExists($data['mission_name'])) {
            return redirect()->back()->withInput()->with('error', 'A mission with this name already exists.');
        }

        if ($this->missionModel->insert($data)) {
            return redirect()->to('admin/missions')->with('success', 'Mission created successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create mission. Please try again.');
        }
    }

    /**
     * Show edit mission form
     */
    public function edit($id = null)
    {
        if (!$id) {
            return redirect()->to('admin/missions')->with('error', 'Mission ID is required.');
        }

        $mission = $this->missionModel->getMissionWithDetails($id);
        
        if (!$mission) {
            return redirect()->to('admin/missions')->with('error', 'Mission not found.');
        }

        $this->data['title'] = 'Edit Mission - ' . $mission['mission_name'];
        $this->data['description'] = 'Edit mission information';
        $this->data['active_menu'] = 'missions';
        
        // Breadcrumbs
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Missions', 'url' => base_url('admin/missions')],
            ['title' => $mission['mission_name'], 'url' => base_url('admin/missions/' . $mission['id'])],
            ['title' => 'Edit', 'url' => '']
        ];
        
        // Page header
        $this->data['page_title'] = 'Edit Mission';
        $this->data['page_description'] = 'Update mission information and details.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/missions/' . $mission['id']) . '" class="btn btn-secondary me-2">
                <i class="fas fa-eye me-2"></i>View Details
            </a>
            <a href="' . base_url('admin/missions') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to List
            </a>
        ';

        // Get buyers (users with is_buyer = true)
        $buyers = $this->userModel->where('is_buyer', true)
                                 ->where('status', 'active')
                                 ->orderBy('fullname', 'ASC')
                                 ->findAll();

        // Get active commodities
        $commodities = $this->commodityModel->getActiveCommodities();

        // Merge old input with mission data
        $this->data['mission'] = [
            'id' => $mission['id'],
            'mission_name' => old('mission_name', $mission['mission_name']),
            'mission_date' => old('mission_date', $mission['mission_date']),
            'mission_status' => old('mission_status', $mission['mission_status']),
            'user_id' => old('user_id', $mission['user_id']),
            'commodity_id' => old('commodity_id', $mission['commodity_id']),
            'budgeted_amount' => old('budgeted_amount', $mission['budgeted_amount']),
            'actual_amount' => old('actual_amount', $mission['actual_amount']),
            'remarks' => old('remarks', $mission['remarks'])
        ];

        $this->data['buyers'] = $buyers;
        $this->data['commodities'] = $commodities;
        $this->data['validation'] = \Config\Services::validation();

        return view('admin/missions/mission_edit', $this->data);
    }

    /**
     * Update mission
     */
    public function update($id = null)
    {
        if (!$id) {
            return redirect()->to('admin/missions')->with('error', 'Mission ID is required.');
        }

        $mission = $this->missionModel->getMissionWithDetails($id);
        
        if (!$mission) {
            return redirect()->to('admin/missions')->with('error', 'Mission not found.');
        }

        $rules = [
            'mission_name' => 'required|min_length[3]|max_length[255]',
            'mission_date' => 'required|valid_date',
            'mission_status' => 'required|in_list[pending,in_progress,completed,cancelled]',
            'user_id' => 'permit_empty|integer',
            'commodity_id' => 'permit_empty|integer',
            'budgeted_amount' => 'permit_empty|decimal',
            'actual_amount' => 'permit_empty|decimal',
            'remarks' => 'permit_empty|max_length[1000]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('validation', $this->validator);
        }

        $data = [
            'mission_name' => $this->request->getPost('mission_name'),
            'mission_date' => $this->request->getPost('mission_date'),
            'mission_status' => $this->request->getPost('mission_status'),
            'user_id' => $this->request->getPost('user_id') ?: null,
            'commodity_id' => $this->request->getPost('commodity_id') ?: null,
            'budgeted_amount' => $this->request->getPost('budgeted_amount') ?: null,
            'actual_amount' => $this->request->getPost('actual_amount') ?: null,
            'remarks' => $this->request->getPost('remarks')
            // mission_number should not be updated after creation
        ];

        // Check if mission name already exists (excluding current mission)
        if ($this->missionModel->missionNameExists($data['mission_name'], $id)) {
            return redirect()->back()->withInput()->with('error', 'A mission with this name already exists.');
        }

        if ($this->missionModel->update($id, $data)) {
            return redirect()->to('admin/missions/' . $id)->with('success', 'Mission updated successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update mission. Please try again.');
        }
    }

    /**
     * Delete mission (soft delete)
     */
    public function delete($id = null)
    {
        if (!$id) {
            return redirect()->to('admin/missions')->with('error', 'Mission ID is required.');
        }

        $mission = $this->missionModel->getActiveMission($id);
        
        if (!$mission) {
            return redirect()->to('admin/missions')->with('error', 'Mission not found.');
        }

        if ($this->missionModel->softDelete($id)) {
            return redirect()->to('admin/missions')->with('success', 'Mission deleted successfully.');
        } else {
            return redirect()->to('admin/missions')->with('error', 'Failed to delete mission. Please try again.');
        }
    }
}
