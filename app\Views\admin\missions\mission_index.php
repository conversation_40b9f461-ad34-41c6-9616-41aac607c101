<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .stats-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
    }

    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .stats-label {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 0;
    }

    .mission-table {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        color: #495057;
        padding: 1rem 0.75rem;
    }

    .table td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
    }

    .status-badge {
        padding: 0.375rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .status-in_progress {
        background-color: #cce5ff;
        color: #004085;
        border: 1px solid #74c0fc;
    }

    .status-completed {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #51cf66;
    }

    .status-cancelled {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #ff6b6b;
    }

    .action-buttons .btn {
        padding: 0.375rem 0.75rem;
        margin: 0 0.125rem;
        border-radius: 6px;
        font-size: 0.875rem;
    }

    .search-filters {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .filter-group {
        margin-bottom: 1rem;
    }

    .filter-group:last-child {
        margin-bottom: 0;
    }

    .pagination-wrapper {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        margin-top: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-primary">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="stats-number"><?= $stats['total_missions'] ?></div>
                <p class="stats-label">Total Missions</p>
            </div>
        </div>
        
        <?php foreach ($stats['status_distribution'] as $status): ?>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-<?= $status['mission_status'] === 'completed' ? 'success' : ($status['mission_status'] === 'in_progress' ? 'info' : ($status['mission_status'] === 'cancelled' ? 'danger' : 'warning')) ?>">
                    <i class="fas fa-<?= $status['mission_status'] === 'completed' ? 'check-circle' : ($status['mission_status'] === 'in_progress' ? 'clock' : ($status['mission_status'] === 'cancelled' ? 'times-circle' : 'hourglass-half')) ?>"></i>
                </div>
                <div class="stats-number"><?= $status['count'] ?></div>
                <p class="stats-label"><?= ucfirst(str_replace('_', ' ', $status['mission_status'])) ?></p>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <!-- Search and Filters -->
    <div class="search-filters">
        <form method="GET" action="<?= base_url('admin/missions') ?>">
            <div class="row">
                <div class="col-md-6">
                    <div class="filter-group">
                        <label for="search" class="form-label">Search Missions</label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="<?= esc($search) ?>" placeholder="Search by mission name or number...">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="filter-group">
                        <label for="status" class="form-label">Filter by Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Statuses</option>
                            <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>Pending</option>
                            <option value="in_progress" <?= $status_filter === 'in_progress' ? 'selected' : '' ?>>In Progress</option>
                            <option value="completed" <?= $status_filter === 'completed' ? 'selected' : '' ?>>Completed</option>
                            <option value="cancelled" <?= $status_filter === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="filter-group">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-admin-primary">
                                <i class="fas fa-search me-2"></i>Search
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Missions Table -->
    <div class="mission-table">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Mission #</th>
                        <th>Mission Name</th>
                        <th>Date</th>
                        <th>Status</th>
                        <th>Remarks</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($missions)): ?>
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-tasks fa-3x mb-3"></i>
                                <p class="mb-0">No missions found.</p>
                                <small>Create your first mission to get started.</small>
                            </div>
                        </td>
                    </tr>
                    <?php else: ?>
                        <?php foreach ($missions as $mission): ?>
                        <tr>
                            <td>
                                <span class="fw-bold text-primary"><?= esc($mission['mission_number']) ?></span>
                            </td>
                            <td>
                                <div class="fw-bold"><?= esc($mission['mission_name']) ?></div>
                            </td>
                            <td>
                                <span class="text-muted">
                                    <?= date('M j, Y', strtotime($mission['mission_date'])) ?>
                                </span>
                            </td>
                            <td>
                                <span class="status-badge status-<?= $mission['mission_status'] ?>">
                                    <?= ucfirst(str_replace('_', ' ', $mission['mission_status'])) ?>
                                </span>
                            </td>
                            <td>
                                <span class="text-muted">
                                    <?= $mission['remarks'] ? esc(substr($mission['remarks'], 0, 50)) . (strlen($mission['remarks']) > 50 ? '...' : '') : '-' ?>
                                </span>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <?= date('M j, Y', strtotime($mission['created_at'])) ?>
                                </small>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="<?= base_url('admin/missions/' . $mission['id']) ?>" 
                                       class="btn btn-sm btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?= base_url('admin/missions/' . $mission['id'] . '/edit') ?>" 
                                       class="btn btn-sm btn-outline-success" title="Edit Mission">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="confirmDelete(<?= $mission['id'] ?>, '<?= esc($mission['mission_name']) ?>')" 
                                            title="Delete Mission">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <?php if ($pager): ?>
    <div class="pagination-wrapper">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
                Showing <?= $pager->getFirstPage() ?> to <?= $pager->getLastPage() ?> of <?= $pager->getPageCount() ?> entries
            </div>
            <div>
                <?= $pager->links() ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the mission "<span id="missionName"></span>"?</p>
                <p class="text-muted small">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete Mission</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function confirmDelete(missionId, missionName) {
    document.getElementById('missionName').textContent = missionName;
    document.getElementById('deleteForm').action = '<?= base_url('admin/missions') ?>/' + missionId + '/delete';
    
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
<?= $this->endSection() ?>
