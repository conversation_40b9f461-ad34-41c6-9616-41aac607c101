<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .stats-card {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
        transition: transform 0.2s;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
    }
    
    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
    }
    
    .stats-icon.bg-primary { background: var(--admin-primary); }
    .stats-icon.bg-success { background: #28a745; }
    .stats-icon.bg-warning { background: #ffc107; color: #000; }
    .stats-icon.bg-info { background: #17a2b8; }
    
    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--admin-dark);
        line-height: 1;
    }
    
    .stats-label {
        color: var(--admin-muted);
        font-size: 0.9rem;
        margin-bottom: 0;
    }
    
    .stats-trend {
        font-size: 0.8rem;
        margin-top: 0.5rem;
    }
    
    .trend-up { color: #28a745; }
    .trend-down { color: #dc3545; }
    
    .recent-activity {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .activity-item {
        display: flex;
        align-items: flex-start;
        padding: 1rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 0.9rem;
        flex-shrink: 0;
    }
    
    .activity-content {
        flex: 1;
    }
    
    .activity-title {
        font-weight: 500;
        color: var(--admin-dark);
        margin-bottom: 0.25rem;
    }
    
    .activity-description {
        color: var(--admin-muted);
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }
    
    .activity-time {
        color: var(--admin-muted);
        font-size: 0.8rem;
    }
    
    .chart-container {
        position: relative;
        height: 300px;
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-primary">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="stats-number">247</div>
            <p class="stats-label">Total Orders</p>
            <div class="stats-trend trend-up">
                <i class="fas fa-arrow-up me-1"></i>12% from last month
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-success">
                <i class="fas fa-seedling"></i>
            </div>
            <div class="stats-number">1,284</div>
            <p class="stats-label">Products Listed</p>
            <div class="stats-trend trend-up">
                <i class="fas fa-arrow-up me-1"></i>8% from last month
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-warning">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-number">892</div>
            <p class="stats-label">Active Users</p>
            <div class="stats-trend trend-down">
                <i class="fas fa-arrow-down me-1"></i>3% from last month
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-info">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stats-number">$24.7K</div>
            <p class="stats-label">Monthly Revenue</p>
            <div class="stats-trend trend-up">
                <i class="fas fa-arrow-up me-1"></i>15% from last month
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Sales Chart -->
    <div class="col-lg-8 mb-4">
        <div class="admin-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Sales Overview</h5>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        Last 7 Days
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">Last 7 Days</a></li>
                        <li><a class="dropdown-item" href="#">Last 30 Days</a></li>
                        <li><a class="dropdown-item" href="#">Last 3 Months</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="salesChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="col-lg-4 mb-4">
        <div class="admin-card">
            <div class="card-header">
                <h5 class="mb-0">Recent Activity</h5>
            </div>
            <div class="card-body p-0">
                <div class="recent-activity">
                    <div class="activity-item" style="padding: 1rem;">
                        <div class="activity-icon bg-success text-white">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">New Product Added</div>
                            <div class="activity-description">Premium Rice variety added by John Farmer</div>
                            <div class="activity-time">2 minutes ago</div>
                        </div>
                    </div>
                    
                    <div class="activity-item" style="padding: 1rem;">
                        <div class="activity-icon bg-primary text-white">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">New Order Received</div>
                            <div class="activity-description">Order #ORD-001247 for 100kg Wheat</div>
                            <div class="activity-time">5 minutes ago</div>
                        </div>
                    </div>
                    
                    <div class="activity-item" style="padding: 1rem;">
                        <div class="activity-icon bg-warning text-dark">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">New User Registration</div>
                            <div class="activity-description">Mike Buyer joined as a new buyer</div>
                            <div class="activity-time">12 minutes ago</div>
                        </div>
                    </div>
                    
                    <div class="activity-item" style="padding: 1rem;">
                        <div class="activity-icon bg-info text-white">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">Payment Completed</div>
                            <div class="activity-description">$1,250 payment for Order #ORD-001245</div>
                            <div class="activity-time">18 minutes ago</div>
                        </div>
                    </div>
                    
                    <div class="activity-item" style="padding: 1rem;">
                        <div class="activity-icon bg-secondary text-white">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">Order Shipped</div>
                            <div class="activity-description">Order #ORD-001243 has been shipped</div>
                            <div class="activity-time">1 hour ago</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer text-center">
                <a href="<?= base_url('admin/activity') ?>" class="btn btn-sm btn-admin-secondary">View All Activity</a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions & Recent Orders -->
<div class="row">
    <!-- Quick Actions -->
    <div class="col-lg-6 mb-4">
        <div class="admin-card">
            <div class="card-header">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-6">
                        <a href="<?= base_url('admin/products/create') ?>" class="btn btn-admin-primary w-100 d-flex flex-column align-items-center p-3">
                            <i class="fas fa-plus-circle fa-2x mb-2"></i>
                            <span>Add Product</span>
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="<?= base_url('admin/users/create') ?>" class="btn btn-admin-secondary w-100 d-flex flex-column align-items-center p-3">
                            <i class="fas fa-user-plus fa-2x mb-2"></i>
                            <span>Add User</span>
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="<?= base_url('admin/orders') ?>" class="btn btn-admin-secondary w-100 d-flex flex-column align-items-center p-3">
                            <i class="fas fa-list-alt fa-2x mb-2"></i>
                            <span>View Orders</span>
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="<?= base_url('admin/analytics') ?>" class="btn btn-admin-secondary w-100 d-flex flex-column align-items-center p-3">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                            <span>Analytics</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Orders -->
    <div class="col-lg-6 mb-4">
        <div class="admin-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Orders</h5>
                <a href="<?= base_url('admin/orders') ?>" class="btn btn-sm btn-admin-primary">View All</a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Amount</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>#ORD-001247</strong></td>
                                <td>John Buyer</td>
                                <td>$450</td>
                                <td><span class="badge bg-warning">Pending</span></td>
                            </tr>
                            <tr>
                                <td><strong>#ORD-001246</strong></td>
                                <td>Mike Smith</td>
                                <td>$320</td>
                                <td><span class="badge bg-success">Completed</span></td>
                            </tr>
                            <tr>
                                <td><strong>#ORD-001245</strong></td>
                                <td>Sarah Johnson</td>
                                <td>$1,250</td>
                                <td><span class="badge bg-info">Processing</span></td>
                            </tr>
                            <tr>
                                <td><strong>#ORD-001244</strong></td>
                                <td>David Brown</td>
                                <td>$890</td>
                                <td><span class="badge bg-success">Completed</span></td>
                            </tr>
                            <tr>
                                <td><strong>#ORD-001243</strong></td>
                                <td>Lisa Wilson</td>
                                <td>$675</td>
                                <td><span class="badge bg-secondary">Shipped</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize sales chart
    const ctx = document.getElementById('salesChart').getContext('2d');
    const salesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [{
                label: 'Sales',
                data: [12, 19, 3, 5, 2, 3, 10],
                borderColor: '#2E7D32',
                backgroundColor: 'rgba(46, 125, 50, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'Orders',
                data: [8, 15, 2, 4, 1, 2, 8],
                borderColor: '#4CAF50',
                backgroundColor: 'rgba(76, 175, 80, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>
<?= $this->endSection() ?>