<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }
    
    .user-info {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .user-details {
        flex: 1;
    }
    
    .user-name {
        font-weight: 600;
        margin-bottom: 2px;
    }
    
    .user-email {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .role-badges {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;
    }
    
    .role-badge {
        font-size: 0.75rem;
        padding: 2px 6px;
    }
    
    .status-badge {
        font-size: 0.75rem;
        padding: 4px 8px;
        border-radius: 12px;
    }
    
    .action-buttons {
        display: flex;
        gap: 4px;
    }
    
    .btn-action {
        width: 32px;
        height: 32px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
    }
    
    .admin-table {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .admin-table table {
        margin-bottom: 0;
    }
    
    .admin-table th {
        background: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        color: #495057;
        padding: 1rem 0.75rem;
    }
    
    .admin-table td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .admin-table tbody tr:hover {
        background-color: #f8f9fa;
    }
    
    .bulk-actions {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        display: none;
    }
    
    .bulk-actions.show {
        display: block;
    }
    
    .filters-section {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .search-box {
        position: relative;
    }
    
    .search-box .fas {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
    }
    
    .search-box input {
        padding-left: 40px;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Filters Section -->
<div class="filters-section">
    <div class="row align-items-center">
        <div class="col-md-4">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" class="form-control" id="searchUsers" placeholder="Search users...">
            </div>
        </div>
        <div class="col-md-2">
            <select class="form-select" id="filterRole">
                <option value="">All Roles</option>
                <option value="admin">Admin</option>
                <option value="supervisor">Supervisor</option>
                <option value="buyer">Buyer</option>
            </select>
        </div>
        <div class="col-md-2">
            <select class="form-select" id="filterStatus">
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="suspended">Suspended</option>
                <option value="pending">Pending</option>
            </select>
        </div>
        <div class="col-md-4 text-end">
            <button class="btn btn-outline-secondary me-2" onclick="resetFilters()">
                <i class="fas fa-undo me-1"></i>Reset
            </button>
            <button class="btn btn-admin-primary" onclick="exportUsers()">
                <i class="fas fa-download me-1"></i>Export
            </button>
        </div>
    </div>
</div>

<!-- Bulk Actions -->
<div class="bulk-actions" id="bulkActions">
    <div class="row align-items-center">
        <div class="col-md-6">
            <span class="fw-bold">
                <span id="selectedCount">0</span> users selected
            </span>
        </div>
        <div class="col-md-6 text-end">
            <button class="btn btn-success btn-sm me-2" id="bulkActivateBtn">
                <i class="fas fa-check me-1"></i>Activate
            </button>
            <button class="btn btn-warning btn-sm me-2" id="bulkDeactivateBtn">
                <i class="fas fa-pause me-1"></i>Deactivate
            </button>
            <button class="btn btn-danger btn-sm" id="bulkDeleteBtn">
                <i class="fas fa-trash me-1"></i>Delete
            </button>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="admin-table">
    <table class="table table-hover" id="usersTable">
        <thead>
            <tr>
                <th width="40">
                    <input type="checkbox" class="form-check-input" id="selectAllHeader">
                </th>
                <th data-sortable>User</th>
                <th data-sortable>Username</th>
                <th>Roles</th>
                <th data-sortable>Status</th>
                <th data-sortable>Joined</th>
                <th data-sortable>Last Login</th>
                <th width="120">Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($users)): ?>
                <?php foreach ($users as $user): ?>
                <tr>
                    <td><input type="checkbox" class="form-check-input row-select" value="<?= $user['id'] ?>"></td>
                    <td>
                        <div class="user-info">
                            <img src="<?= base_url('public/assets/images/default-avatar.svg') ?>" 
                                 alt="<?= esc($user['fullname']) ?>" class="user-avatar">
                            <div class="user-details">
                                <div class="user-name"><?= esc($user['fullname']) ?></div>
                                <div class="user-email"><?= esc($user['email']) ?></div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="fw-bold"><?= esc($user['username']) ?></span>
                    </td>
                    <td>
                        <div class="role-badges">
                            <?php if (isset($user['is_admin']) && $user['is_admin'] === true): ?>
                                <span class="badge bg-danger role-badge">Admin</span>
                            <?php endif; ?>
                            <?php if (isset($user['is_supervisor']) && $user['is_supervisor'] === true): ?>
                                <span class="badge bg-warning role-badge">Supervisor</span>
                            <?php endif; ?>
                            <?php if (isset($user['is_buyer']) && $user['is_buyer'] === true): ?>
                                <span class="badge bg-info role-badge">Buyer</span>
                            <?php endif; ?>
                        </div>
                    </td>
                    <td>
                        <?php
                        $statusClass = match($user['status']) {
                            'active' => 'bg-success',
                            'inactive' => 'bg-secondary',
                            'suspended' => 'bg-danger',
                            'pending' => 'bg-warning',
                            default => 'bg-secondary'
                        };
                        ?>
                        <span class="badge <?= $statusClass ?> status-badge"><?= ucfirst(esc($user['status'])) ?></span>
                    </td>
                    <td>
                        <div><?= date('M j, Y', strtotime($user['created_at'])) ?></div>
                        <small class="text-muted"><?= date('g:i A', strtotime($user['created_at'])) ?></small>
                    </td>
                    <td>
                        <div class="text-muted">Never</div>
                        <small class="text-muted">-</small>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline-primary btn-action" onclick="viewUser(<?= $user['id'] ?>)" title="View">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary btn-action" onclick="editUser(<?= $user['id'] ?>)" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger btn-action" onclick="deleteUser(<?= $user['id'] ?>)" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="text-muted">
                            <i class="fas fa-users fa-3x mb-3"></i>
                            <h5>No Users Found</h5>
                            <p>No users have been created yet. Use the "Create User" button above to add your first user.</p>
                        </div>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<!-- Pagination -->
<?php if (!empty($users) && count($users) >= 20): ?>
<div class="d-flex justify-content-between align-items-center mt-4">
    <div class="text-muted">
        Showing 1 to <?= count($users) ?> of <?= $total_users ?? count($users) ?> users
    </div>
    <nav>
        <ul class="pagination pagination-sm mb-0">
            <li class="page-item disabled">
                <span class="page-link">Previous</span>
            </li>
            <li class="page-item active">
                <span class="page-link">1</span>
            </li>
            <li class="page-item">
                <a class="page-link" href="#">2</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="#">3</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="#">Next</a>
            </li>
        </ul>
    </nav>
</div>
<?php endif; ?>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize data table functionality
    AdminTemplate.initializeDataTable('usersTable');

    // Handle select all functionality
    const selectAll = document.getElementById('selectAll');
    const selectAllHeader = document.getElementById('selectAllHeader');
    const rowSelects = document.querySelectorAll('.row-select');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');

    function updateBulkActions() {
        const selected = document.querySelectorAll('.row-select:checked');
        if (selected.length > 0) {
            bulkActions.classList.add('show');
            selectedCount.textContent = selected.length;
        } else {
            bulkActions.classList.remove('show');
        }
    }

    // Select all functionality
    if (selectAllHeader) {
        selectAllHeader.addEventListener('change', function() {
            rowSelects.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }

    // Individual row selection
    rowSelects.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActions();

            // Update select all checkbox
            const allChecked = Array.from(rowSelects).every(cb => cb.checked);
            const someChecked = Array.from(rowSelects).some(cb => cb.checked);

            if (selectAllHeader) {
                selectAllHeader.checked = allChecked;
                selectAllHeader.indeterminate = someChecked && !allChecked;
            }
        });
    });

    // Search functionality
    const searchInput = document.getElementById('searchUsers');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('#usersTable tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });
    }

    // Filter functionality
    const roleFilter = document.getElementById('filterRole');
    const statusFilter = document.getElementById('filterStatus');

    function applyFilters() {
        const roleValue = roleFilter?.value.toLowerCase() || '';
        const statusValue = statusFilter?.value.toLowerCase() || '';
        const rows = document.querySelectorAll('#usersTable tbody tr');

        rows.forEach(row => {
            const roleCell = row.querySelector('td:nth-child(4)');
            const statusCell = row.querySelector('td:nth-child(5)');

            if (!roleCell || !statusCell) return;

            const roleText = roleCell.textContent.toLowerCase();
            const statusText = statusCell.textContent.toLowerCase();

            const roleMatch = !roleValue || roleText.includes(roleValue);
            const statusMatch = !statusValue || statusText.includes(statusValue);

            row.style.display = (roleMatch && statusMatch) ? '' : 'none';
        });
    }

    if (roleFilter) {
        roleFilter.addEventListener('change', applyFilters);
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
    }

    // Bulk actions - Standard form submission
    document.getElementById('bulkActivateBtn')?.addEventListener('click', function() {
        const selected = Array.from(document.querySelectorAll('.row-select:checked')).map(cb => cb.value);
        if (selected.length > 0) {
            AdminTemplate.confirmAction(`Activate ${selected.length} selected users?`, function() {
                submitBulkAction('activate', selected);
            });
        }
    });

    document.getElementById('bulkDeactivateBtn')?.addEventListener('click', function() {
        const selected = Array.from(document.querySelectorAll('.row-select:checked')).map(cb => cb.value);
        if (selected.length > 0) {
            AdminTemplate.confirmAction(`Deactivate ${selected.length} selected users?`, function() {
                submitBulkAction('deactivate', selected);
            });
        }
    });

    document.getElementById('bulkDeleteBtn')?.addEventListener('click', function() {
        const selected = Array.from(document.querySelectorAll('.row-select:checked')).map(cb => cb.value);
        if (selected.length > 0) {
            AdminTemplate.confirmAction(`Delete ${selected.length} selected users? This action cannot be undone.`, function() {
                submitBulkAction('delete', selected);
            });
        }
    });
});

// Function to submit bulk actions using standard form submission
function submitBulkAction(action, userIds) {
    // Create a form to submit the bulk action
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '<?= base_url('admin/users/bulk-action') ?>';

    // Add CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '<?= csrf_token() ?>';
    csrfInput.value = '<?= csrf_hash() ?>';
    form.appendChild(csrfInput);

    // Add action
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = action;
    form.appendChild(actionInput);

    // Add user IDs
    userIds.forEach(function(userId) {
        const userInput = document.createElement('input');
        userInput.type = 'hidden';
        userInput.name = 'user_ids[]';
        userInput.value = userId;
        form.appendChild(userInput);
    });

    // Submit the form
    document.body.appendChild(form);
    form.submit();
}

function resetFilters() {
    document.getElementById('searchUsers').value = '';
    document.getElementById('filterRole').value = '';
    document.getElementById('filterStatus').value = '';

    // Show all rows
    const rows = document.querySelectorAll('#usersTable tbody tr');
    rows.forEach(row => {
        row.style.display = '';
    });
}

function exportUsers() {
    // Create a form to submit the export request
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '<?= base_url('admin/users/export') ?>';

    // Add CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '<?= csrf_token() ?>';
    csrfInput.value = '<?= csrf_hash() ?>';
    form.appendChild(csrfInput);

    // Add current filters if any
    const searchValue = document.getElementById('searchUsers')?.value || '';
    const roleFilter = document.getElementById('filterRole')?.value || '';
    const statusFilter = document.getElementById('filterStatus')?.value || '';

    if (searchValue) {
        const searchInput = document.createElement('input');
        searchInput.type = 'hidden';
        searchInput.name = 'search';
        searchInput.value = searchValue;
        form.appendChild(searchInput);
    }

    if (roleFilter) {
        const roleInput = document.createElement('input');
        roleInput.type = 'hidden';
        roleInput.name = 'role_filter';
        roleInput.value = roleFilter;
        form.appendChild(roleInput);
    }

    if (statusFilter) {
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status_filter';
        statusInput.value = statusFilter;
        form.appendChild(statusInput);
    }

    // Submit the form
    document.body.appendChild(form);
    form.submit();
}

function viewUser(id) {
    window.location.href = `<?= base_url('admin/users/') ?>${id}`;
}

function editUser(id) {
    window.location.href = `<?= base_url('admin/users/') ?>${id}/edit`;
}

function deleteUser(id) {
    AdminTemplate.confirmAction('Are you sure you want to delete this user? This action cannot be undone.', function() {
        // Create a form to submit DELETE request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?= base_url('admin/users/') ?>${id}`;

        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '<?= csrf_token() ?>';
        csrfInput.value = '<?= csrf_hash() ?>';
        form.appendChild(csrfInput);

        // Add method override for DELETE
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        // Submit the form
        document.body.appendChild(form);
        form.submit();
    });
}
</script>
<?= $this->endSection() ?>
