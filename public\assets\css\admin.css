/* Admin Template Styles for DCBuyer */

:root {
    --admin-sidebar-width: 260px;
    --admin-sidebar-collapsed-width: 70px;
    --admin-topbar-height: 70px;
    --admin-primary: #2E7D32;
    --admin-secondary: #4CAF50;
    --admin-dark: #1A365D;
    --admin-light: #f8f9fa;
    --admin-border: #e9ecef;
    --admin-text: #495057;
    --admin-muted: #6c757d;
    --admin-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --admin-sidebar-bg: #ffffff;
    --admin-sidebar-text: #495057;
    --admin-sidebar-hover: #f8f9fa;
    --admin-sidebar-active: #2E7D32;
}

/* Admin Body */
.admin-body {
    font-family: 'Inter', sans-serif;
    background-color: var(--admin-light);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: var(--admin-sidebar-width);
    background: var(--admin-sidebar-bg);
    border-right: 1px solid var(--admin-border);
    box-shadow: var(--admin-shadow);
    transition: all 0.3s ease;
    z-index: 1050;
    display: flex;
    flex-direction: column;
}

.sidebar.collapsed {
    width: var(--admin-sidebar-collapsed-width);
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid var(--admin-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: var(--admin-topbar-height);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--admin-dark);
    font-weight: 700;
    font-size: 1.2rem;
}

.sidebar-logo {
    width: 35px;
    height: 35px;
    margin-right: 10px;
}

.sidebar.collapsed .sidebar-brand-text {
    display: none;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--admin-muted);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s;
}

.sidebar-toggle:hover {
    background: var(--admin-sidebar-hover);
    color: var(--admin-dark);
}

/* Sidebar Menu */
.sidebar-menu {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 0;
}

.sidebar-menu .nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--admin-sidebar-text);
    text-decoration: none;
    border-radius: 0;
    transition: all 0.2s;
    position: relative;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.sidebar-menu .nav-link:hover {
    background: var(--admin-sidebar-hover);
    color: var(--admin-dark);
}

.sidebar-menu .nav-link.active {
    background: rgba(46, 125, 50, 0.1);
    color: var(--admin-sidebar-active);
    border-right: 3px solid var(--admin-sidebar-active);
}

.sidebar-menu .nav-link i {
    width: 20px;
    margin-right: 12px;
    text-align: center;
    font-size: 1rem;
}

.sidebar.collapsed .nav-link span {
    display: none;
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 0.75rem;
}

.sidebar.collapsed .nav-link i {
    margin-right: 0;
}

/* Submenu */
.submenu {
    background: rgba(0, 0, 0, 0.02);
    border-left: 2px solid var(--admin-border);
    margin-left: 2rem;
}

.submenu .nav-link {
    padding: 0.5rem 1rem 0.5rem 2rem;
    font-size: 0.9rem;
}

.sidebar.collapsed .submenu {
    display: none;
}

/* Badge in sidebar */
.sidebar-menu .badge {
    margin-left: auto;
    font-size: 0.75rem;
}

.sidebar.collapsed .badge {
    display: none;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid var(--admin-border);
}

.user-info {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    object-fit: cover;
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--admin-dark);
    line-height: 1.2;
}

.user-role {
    font-size: 0.8rem;
    color: var(--admin-muted);
}

.sidebar.collapsed .user-details {
    display: none;
}

.sidebar.collapsed .user-info {
    justify-content: center;
}

/* Main Content */
.main-content {
    margin-left: var(--admin-sidebar-width);
    min-height: 100vh;
    transition: margin-left 0.3s ease;
    display: flex;
    flex-direction: column;
}

.main-content.sidebar-collapsed {
    margin-left: var(--admin-sidebar-collapsed-width);
}

/* Topbar */
.topbar {
    background: white;
    height: var(--admin-topbar-height);
    box-shadow: var(--admin-shadow);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
    position: sticky;
    top: 0;
    z-index: 1040;
}

.topbar-left {
    display: flex;
    align-items: center;
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-collapse {
    background: none;
    border: none;
    color: var(--admin-muted);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s;
    margin-right: 1rem;
}

.sidebar-collapse:hover {
    background: var(--admin-sidebar-hover);
    color: var(--admin-dark);
}

/* Breadcrumb */
.breadcrumb-wrapper {
    margin-left: 1rem;
}

.breadcrumb {
    margin: 0;
    background: none;
    padding: 0;
}

.breadcrumb-item {
    font-size: 0.9rem;
}

.breadcrumb-item a {
    color: var(--admin-muted);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: var(--admin-primary);
}

.breadcrumb-item.active {
    color: var(--admin-dark);
    font-weight: 500;
}

/* Search */
.search-wrapper {
    max-width: 300px;
}

.search-input {
    border: 1px solid var(--admin-border);
    border-radius: 25px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.search-input:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
}

/* Notifications */
.notification-btn {
    position: relative;
    color: var(--admin-muted);
    font-size: 1.2rem;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.2s;
}

.notification-btn:hover {
    background: var(--admin-sidebar-hover);
    color: var(--admin-dark);
}

.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-menu {
    width: 320px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--admin-border);
}

.notification-item:hover {
    background: var(--admin-sidebar-hover);
}

.notification-content {
    display: flex;
    flex-direction: column;
}

.notification-title {
    font-weight: 500;
    font-size: 0.9rem;
    color: var(--admin-dark);
}

.notification-time {
    font-size: 0.8rem;
    color: var(--admin-muted);
    margin-top: 0.25rem;
}

/* User Dropdown */
.user-btn {
    display: flex;
    align-items: center;
    color: var(--admin-dark);
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 25px;
    transition: all 0.2s;
}

.user-btn:hover {
    background: var(--admin-sidebar-hover);
}

.user-avatar-small {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 0.5rem;
    object-fit: cover;
}

/* Page Content */
.page-content {
    flex: 1;
    padding: 2rem 0;
}

.page-header {
    margin-bottom: 2rem;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.page-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--admin-dark);
    margin: 0;
}

.page-description {
    color: var(--admin-muted);
    margin: 0.5rem 0 0 0;
    font-size: 1rem;
}

.page-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* Cards */
.admin-card {
    background: white;
    border-radius: 8px;
    box-shadow: var(--admin-shadow);
    border: 1px solid var(--admin-border);
    margin-bottom: 1.5rem;
}

.admin-card .card-header {
    background: white;
    border-bottom: 1px solid var(--admin-border);
    padding: 1rem 1.5rem;
    font-weight: 600;
    color: var(--admin-dark);
}

.admin-card .card-body {
    padding: 1.5rem;
}

/* Buttons */
.btn-admin-primary {
    background: var(--admin-primary);
    border-color: var(--admin-primary);
    color: white;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.2s;
}

.btn-admin-primary:hover {
    background: var(--admin-secondary);
    border-color: var(--admin-secondary);
    color: white;
    transform: translateY(-1px);
}

.btn-admin-secondary {
    background: transparent;
    border-color: var(--admin-border);
    color: var(--admin-text);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.2s;
}

.btn-admin-secondary:hover {
    background: var(--admin-sidebar-hover);
    border-color: var(--admin-primary);
    color: var(--admin-primary);
}

/* Tables */
.admin-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--admin-shadow);
}

.admin-table .table {
    margin: 0;
}

.admin-table .table th {
    background: var(--admin-light);
    border-bottom: 1px solid var(--admin-border);
    font-weight: 600;
    color: var(--admin-dark);
    padding: 1rem;
}

.admin-table .table td {
    padding: 1rem;
    border-bottom: 1px solid var(--admin-border);
    color: var(--admin-text);
}

.admin-table .table tbody tr:hover {
    background: rgba(46, 125, 50, 0.02);
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .main-content.sidebar-collapsed {
        margin-left: 0;
    }
    
    .page-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .page-actions {
        justify-content: flex-start;
    }
}

@media (max-width: 767.98px) {
    .topbar {
        padding: 0 1rem;
    }
    
    .search-wrapper {
        display: none !important;
    }
    
    .page-content {
        padding: 1rem 0;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--admin-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    :root {
        --admin-sidebar-bg: #2d3748;
        --admin-sidebar-text: #e2e8f0;
        --admin-sidebar-hover: #4a5568;
        --admin-light: #1a202c;
        --admin-border: #4a5568;
        --admin-text: #e2e8f0;
        --admin-dark: #f7fafc;
    }
    
    .admin-body {
        background-color: var(--admin-light);
        color: var(--admin-text);
    }
}