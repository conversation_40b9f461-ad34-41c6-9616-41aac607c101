<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .form-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
    }

    .form-header {
        border-bottom: 2px solid #f8f9fa;
        padding-bottom: 1.5rem;
        margin-bottom: 2rem;
    }

    .form-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
    }

    .form-subtitle {
        color: #6c757d;
        font-size: 1rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
    }

    .form-label i {
        margin-right: 0.5rem;
        color: var(--bs-primary);
    }

    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
    }

    .form-text {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .is-invalid {
        border-color: #dc3545;
    }

    .invalid-feedback {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .required {
        color: #dc3545;
    }

    .btn-group-custom {
        gap: 0.5rem;
    }

    .status-option {
        padding: 0.75rem 1rem;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        text-align: center;
    }

    .status-option:hover {
        border-color: var(--bs-primary);
        background-color: rgba(var(--bs-primary-rgb), 0.1);
    }

    .status-option.active {
        border-color: var(--bs-primary);
        background-color: var(--bs-primary);
        color: white;
    }

    .status-option input[type="radio"] {
        display: none;
    }

    .status-icon {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
        display: block;
    }

    .status-label {
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .mission-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid var(--bs-primary);
    }

    .mission-info h6 {
        color: #495057;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .mission-info small {
        color: #6c757d;
        display: block;
    }

    @media (max-width: 768px) {
        .form-card {
            padding: 1.5rem;
        }
        
        .status-options {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="form-card">
        <div class="form-header">
            <div class="form-title">
                <i class="fas fa-edit me-2"></i>
                Edit Mission
            </div>
            <div class="form-subtitle">
                Update mission information and details
            </div>
        </div>

        <!-- Mission Info -->
        <div class="mission-info">
            <h6>Mission Information</h6>
            <small><strong>Mission ID:</strong> #<?= $mission['id'] ?></small>
            <small><strong>Created:</strong> <?= date('M j, Y \a\t g:i A', strtotime($mission['created_at'] ?? '')) ?></small>
        </div>

        <form action="<?= base_url('admin/missions/' . $mission['id'] . '/update') ?>" method="POST" novalidate>
            <?= csrf_field() ?>
            <input type="hidden" name="_method" value="PUT">
            
            <div class="row">
                <div class="col-lg-8">
                    <!-- Mission Name -->
                    <div class="form-group">
                        <label for="mission_name" class="form-label">
                            <i class="fas fa-tag"></i>
                            Mission Name <span class="required">*</span>
                        </label>
                        <input type="text" 
                               class="form-control <?= $validation && $validation->hasError('mission_name') ? 'is-invalid' : '' ?>" 
                               id="mission_name" 
                               name="mission_name" 
                               value="<?= esc($mission['mission_name']) ?>"
                               placeholder="Enter mission name"
                               required>
                        <?php if ($validation && $validation->hasError('mission_name')): ?>
                            <div class="invalid-feedback">
                                <?= $validation->getError('mission_name') ?>
                            </div>
                        <?php endif; ?>
                        <div class="form-text">
                            Provide a clear and descriptive name for the mission
                        </div>
                    </div>

                    <!-- Mission Date -->
                    <div class="form-group">
                        <label for="mission_date" class="form-label">
                            <i class="fas fa-calendar"></i>
                            Mission Date <span class="required">*</span>
                        </label>
                        <input type="date" 
                               class="form-control <?= $validation && $validation->hasError('mission_date') ? 'is-invalid' : '' ?>" 
                               id="mission_date" 
                               name="mission_date" 
                               value="<?= esc($mission['mission_date']) ?>"
                               required>
                        <?php if ($validation && $validation->hasError('mission_date')): ?>
                            <div class="invalid-feedback">
                                <?= $validation->getError('mission_date') ?>
                            </div>
                        <?php endif; ?>
                        <div class="form-text">
                            Select the date when this mission is scheduled
                        </div>
                    </div>

                    <!-- Mission Status -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-info-circle"></i>
                            Mission Status <span class="required">*</span>
                        </label>
                        <div class="row status-options">
                            <div class="col-md-3 col-6 mb-2">
                                <label class="status-option <?= $mission['mission_status'] === 'pending' ? 'active' : '' ?>">
                                    <input type="radio" name="mission_status" value="pending" 
                                           <?= $mission['mission_status'] === 'pending' ? 'checked' : '' ?> required>
                                    <i class="fas fa-hourglass-half status-icon"></i>
                                    <div class="status-label">Pending</div>
                                </label>
                            </div>
                            <div class="col-md-3 col-6 mb-2">
                                <label class="status-option <?= $mission['mission_status'] === 'in_progress' ? 'active' : '' ?>">
                                    <input type="radio" name="mission_status" value="in_progress" 
                                           <?= $mission['mission_status'] === 'in_progress' ? 'checked' : '' ?>>
                                    <i class="fas fa-clock status-icon"></i>
                                    <div class="status-label">In Progress</div>
                                </label>
                            </div>
                            <div class="col-md-3 col-6 mb-2">
                                <label class="status-option <?= $mission['mission_status'] === 'completed' ? 'active' : '' ?>">
                                    <input type="radio" name="mission_status" value="completed" 
                                           <?= $mission['mission_status'] === 'completed' ? 'checked' : '' ?>>
                                    <i class="fas fa-check-circle status-icon"></i>
                                    <div class="status-label">Completed</div>
                                </label>
                            </div>
                            <div class="col-md-3 col-6 mb-2">
                                <label class="status-option <?= $mission['mission_status'] === 'cancelled' ? 'active' : '' ?>">
                                    <input type="radio" name="mission_status" value="cancelled" 
                                           <?= $mission['mission_status'] === 'cancelled' ? 'checked' : '' ?>>
                                    <i class="fas fa-times-circle status-icon"></i>
                                    <div class="status-label">Cancelled</div>
                                </label>
                            </div>
                        </div>
                        <?php if ($validation && $validation->hasError('mission_status')): ?>
                            <div class="invalid-feedback">
                                <?= $validation->getError('mission_status') ?>
                            </div>
                        <?php endif; ?>
                        <div class="form-text">
                            Select the current status of the mission
                        </div>
                    </div>

                    <!-- Remarks -->
                    <div class="form-group">
                        <label for="remarks" class="form-label">
                            <i class="fas fa-comment-alt"></i>
                            Remarks
                        </label>
                        <textarea class="form-control <?= $validation && $validation->hasError('remarks') ? 'is-invalid' : '' ?>" 
                                  id="remarks" 
                                  name="remarks" 
                                  rows="4" 
                                  placeholder="Enter any additional notes or remarks about this mission"><?= esc($mission['remarks']) ?></textarea>
                        <?php if ($validation && $validation->hasError('remarks')): ?>
                            <div class="invalid-feedback">
                                <?= $validation->getError('remarks') ?>
                            </div>
                        <?php endif; ?>
                        <div class="form-text">
                            Optional: Add any additional information or notes about this mission
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Help Card -->
                    <div class="card border-primary">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                Update Guidelines
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Review all information carefully
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Update status as mission progresses
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Adjust dates if necessary
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Add progress notes in remarks
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card border-secondary mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="<?= base_url('admin/missions/' . $mission['id']) ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-2"></i>View Details
                                </a>
                                <a href="<?= base_url('admin/missions') ?>" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-list me-2"></i>All Missions
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-content-between align-items-center mt-4 pt-3 border-top">
                <div class="text-muted">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        Fields marked with <span class="required">*</span> are required
                    </small>
                </div>
                <div class="btn-group-custom d-flex">
                    <a href="<?= base_url('admin/missions/' . $mission['id']) ?>" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-admin-primary">
                        <i class="fas fa-save me-2"></i>Update Mission
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle status option selection
    const statusOptions = document.querySelectorAll('.status-option');
    const statusRadios = document.querySelectorAll('input[name="mission_status"]');
    
    statusOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove active class from all options
            statusOptions.forEach(opt => opt.classList.remove('active'));
            
            // Add active class to clicked option
            this.classList.add('active');
            
            // Check the radio button
            const radio = this.querySelector('input[type="radio"]');
            if (radio) {
                radio.checked = true;
            }
        });
    });
});
</script>
<?= $this->endSection() ?>
