# User Story: Assign Users to Mission

## Epic
Mission Management System

## User Story
**As an** admin/manager  
**I want to** assign users (buyers) to missions with specific commodities and budget allocations  
**So that** I can control who can make purchases for which commodities within defined budget limits  

## Acceptance Criteria

### Functional Requirements
1. **GIVEN** I am on the mission details page (`/admin/missions/1`)  
   **WHEN** I click the "Assign User" button  
   **THEN** a modal popup should appear with an assignment form

2. **GIVEN** the assignment modal is open  
   **WHEN** I view the form  
   **THEN** I should see:
   - Dropdown to select a buyer (users with 'buyer' role)
   - Dropdown to select a commodity
   - Input field for budget amount
   - Input field for actual amount (defaulted to 0.00)
   - Textarea for remarks (optional)
   - "Assign" and "Cancel" buttons

3. **GIVEN** I fill in all required fields (buyer, commodity, budget)  
   **WHEN** I click "Assign"  
   **THEN** the assignment should be saved to `user_commodity_missions` table  
   **AND** I should see a success message  
   **AND** the modal should close  
   **AND** the assignments list should refresh

4. **GIVEN** I try to assign the same buyer-commodity combination  
   **WHEN** I submit the form  
   **THEN** I should see an error message "This buyer is already assigned to this commodity for this mission"

5. **GIVEN** I am viewing the mission details page  
   **WHEN** the page loads  
   **THEN** I should see a list of current assignments showing:
   - Buyer name
   - Commodity name
   - Budget amount
   - Actual amount
   - Remarks
   - Actions (Edit/Remove)

### Technical Requirements
- Use CodeIgniter 4 framework
- Follow RESTful routing conventions
- Use standard form submission (no AJAX)
- Implement proper validation
- Use admin template system
- Follow MVC architecture

## Database Schema

### Migration: `user_commodity_missions` Table
```sql
CREATE TABLE user_commodity_missions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    mission_id BIGINT UNSIGNED NOT NULL,
    commodity_id BIGINT UNSIGNED NOT NULL,
    budget DECIMAL(15,2) NOT NULL,
    actual_amount DECIMAL(15,2) DEFAULT 0.00,
    remarks TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (mission_id) REFERENCES missions(id) ON DELETE CASCADE,
    FOREIGN KEY (commodity_id) REFERENCES commodities(id) ON DELETE CASCADE,
    UNIQUE KEY unique_assignment (user_id, mission_id, commodity_id)
);
```

## Technical Implementation

### 1. Migration File
**File:** `app/Database/Migrations/2025-01-XX-XXXXXX_CreateUserCommodityMissionsTable.php`

```php
<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateUserCommodityMissionsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type'           => 'BIGINT',
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type'       => 'BIGINT',
                'unsigned'   => true,
            ],
            'mission_id' => [
                'type'       => 'BIGINT',
                'unsigned'   => true,
            ],
            'commodity_id' => [
                'type'       => 'BIGINT',
                'unsigned'   => true,
            ],
            'budget' => [
                'type'       => 'DECIMAL',
                'constraint' => '15,2',
            ],
            'actual_amount' => [
                'type'       => 'DECIMAL',
                'constraint' => '15,2',
                'default'    => 0.00,
            ],
            'remarks' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'created_at' => [
                'type'    => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type'    => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
                'update'  => 'CURRENT_TIMESTAMP',
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('mission_id', 'missions', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('commodity_id', 'commodities', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addUniqueKey(['user_id', 'mission_id', 'commodity_id'], 'unique_assignment');
        
        $this->forge->createTable('user_commodity_missions');
    }

    public function down()
    {
        $this->forge->dropTable('user_commodity_missions');
    }
}
```

### 2. Model
**File:** `app/Models/UserCommodityMissionModel.php`

```php
<?php

namespace App\Models;

use CodeIgniter\Model;

class UserCommodityMissionModel extends Model
{
    protected $table = 'user_commodity_missions';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'user_id',
        'mission_id', 
        'commodity_id',
        'budget',
        'actual_amount',
        'remarks'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'user_id'      => 'required|is_natural_no_zero',
        'mission_id'   => 'required|is_natural_no_zero',
        'commodity_id' => 'required|is_natural_no_zero',
        'budget'       => 'required|decimal|greater_than[0]',
        'actual_amount' => 'permit_empty|decimal|greater_than_equal_to[0]',
        'remarks'      => 'permit_empty|string|max_length[1000]'
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'Please select a buyer.',
            'is_natural_no_zero' => 'Invalid buyer selection.'
        ],
        'mission_id' => [
            'required' => 'Mission ID is required.',
            'is_natural_no_zero' => 'Invalid mission ID.'
        ],
        'commodity_id' => [
            'required' => 'Please select a commodity.',
            'is_natural_no_zero' => 'Invalid commodity selection.'
        ],
        'budget' => [
            'required' => 'Budget amount is required.',
            'decimal' => 'Budget must be a valid decimal number.',
            'greater_than' => 'Budget must be greater than 0.'
        ]
    ];

    /**
     * Get assignments for a specific mission with related data
     */
    public function getAssignmentsByMission($missionId)
    {
        return $this->select('
            user_commodity_missions.*,
            users.first_name,
            users.last_name,
            users.email,
            commodities.name as commodity_name,
            commodities.code as commodity_code
        ')
        ->join('users', 'users.id = user_commodity_missions.user_id')
        ->join('commodities', 'commodities.id = user_commodity_missions.commodity_id')
        ->where('mission_id', $missionId)
        ->orderBy('created_at', 'DESC')
        ->findAll();
    }

    /**
     * Check if assignment already exists
     */
    public function assignmentExists($userId, $missionId, $commodityId)
    {
        return $this->where([
            'user_id' => $userId,
            'mission_id' => $missionId,
            'commodity_id' => $commodityId
        ])->first() !== null;
    }

    /**
     * Get assignment by ID with related data
     */
    public function getAssignmentWithDetails($id)
    {
        return $this->select('
            user_commodity_missions.*,
            users.first_name,
            users.last_name,
            users.email,
            commodities.name as commodity_name,
            commodities.code as commodity_code,
            missions.name as mission_name
        ')
        ->join('users', 'users.id = user_commodity_missions.user_id')
        ->join('commodities', 'commodities.id = user_commodity_missions.commodity_id')
        ->join('missions', 'missions.id = user_commodity_missions.mission_id')
        ->where('user_commodity_missions.id', $id)
        ->first();
    }
}
```

### 3. Updated Mission Controller
**File:** `app/Controllers/Admin/MissionController.php`

```php
<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\MissionModel;
use App\Models\UserModel;
use App\Models\CommodityModel;
use App\Models\UserCommodityMissionModel;

class MissionController extends BaseController
{
    protected $missionModel;
    protected $userModel;
    protected $commodityModel;
    protected $assignmentModel;

    public function __construct()
    {
        $this->missionModel = new MissionModel();
        $this->userModel = new UserModel();
        $this->commodityModel = new CommodityModel();
        $this->assignmentModel = new UserCommodityMissionModel();
    }

    /**
     * Display mission details with assignments
     */
    public function show($id = null)
    {
        if (!$id) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Mission not found');
        }

        $mission = $this->missionModel->find($id);
        if (!$mission) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Mission not found');
        }

        // Get current assignments
        $assignments = $this->assignmentModel->getAssignmentsByMission($id);

        // Get buyers for assignment dropdown
        $buyers = $this->userModel->where('role', 'buyer')
                                 ->where('status', 'active')
                                 ->orderBy('first_name', 'ASC')
                                 ->findAll();

        // Get active commodities for assignment dropdown
        $commodities = $this->commodityModel->where('status', 'active')
                                           ->orderBy('name', 'ASC')
                                           ->findAll();

        $data = [
            'title' => 'Mission Details: ' . $mission['name'],
            'mission' => $mission,
            'assignments' => $assignments,
            'buyers' => $buyers,
            'commodities' => $commodities,
            'validation' => \Config\Services::validation()
        ];

        return view('admin/missions/show', $data);
    }

    /**
     * Store new user assignment to mission
     */
    public function storeAssignment($missionId = null)
    {
        if (!$missionId) {
            return redirect()->back()->with('error', 'Invalid mission ID');
        }

        // Verify mission exists
        $mission = $this->missionModel->find($missionId);
        if (!$mission) {
            return redirect()->back()->with('error', 'Mission not found');
        }

        $assignmentData = [
            'user_id' => $this->request->getPost('user_id'),
            'mission_id' => $missionId,
            'commodity_id' => $this->request->getPost('commodity_id'),
            'budget' => $this->request->getPost('budget'),
            'actual_amount' => $this->request->getPost('actual_amount') ?: 0.00,
            'remarks' => $this->request->getPost('remarks')
        ];

        // Check if assignment already exists
        if ($this->assignmentModel->assignmentExists(
            $assignmentData['user_id'], 
            $assignmentData['mission_id'], 
            $assignmentData['commodity_id']
        )) {
            return redirect()->back()
                          ->withInput()
                          ->with('error', 'This buyer is already assigned to this commodity for this mission');
        }

        // Validate and save
        if ($this->assignmentModel->save($assignmentData)) {
            return redirect()->to("/admin/missions/{$missionId}")
                          ->with('success', 'User assigned to mission successfully');
        } else {
            return redirect()->back()
                          ->withInput()
                          ->with('error', 'Failed to assign user to mission')
                          ->with('validation', $this->assignmentModel->errors());
        }
    }

    /**
     * Update existing assignment
     */
    public function updateAssignment($missionId = null, $assignmentId = null)
    {
        if (!$missionId || !$assignmentId) {
            return redirect()->back()->with('error', 'Invalid parameters');
        }

        $assignment = $this->assignmentModel->find($assignmentId);
        if (!$assignment || $assignment['mission_id'] != $missionId) {
            return redirect()->back()->with('error', 'Assignment not found');
        }

        $assignmentData = [
            'budget' => $this->request->getPost('budget'),
            'actual_amount' => $this->request->getPost('actual_amount'),
            'remarks' => $this->request->getPost('remarks')
        ];

        if ($this->assignmentModel->update($assignmentId, $assignmentData)) {
            return redirect()->to("/admin/missions/{$missionId}")
                          ->with('success', 'Assignment updated successfully');
        } else {
            return redirect()->back()
                          ->withInput()
                          ->with('error', 'Failed to update assignment')
                          ->with('validation', $this->assignmentModel->errors());
        }
    }

    /**
     * Remove assignment
     */
    public function removeAssignment($missionId = null, $assignmentId = null)
    {
        if (!$missionId || !$assignmentId) {
            return redirect()->back()->with('error', 'Invalid parameters');
        }

        $assignment = $this->assignmentModel->find($assignmentId);
        if (!$assignment || $assignment['mission_id'] != $missionId) {
            return redirect()->back()->with('error', 'Assignment not found');
        }

        if ($this->assignmentModel->delete($assignmentId)) {
            return redirect()->to("/admin/missions/{$missionId}")
                          ->with('success', 'Assignment removed successfully');
        } else {
            return redirect()->back()->with('error', 'Failed to remove assignment');
        }
    }
}
```

### 4. Routes Configuration
**File:** `app/Config/Routes.php` (Add these routes)

```php
// Mission assignment routes
$routes->get('admin/missions/(:num)', 'Admin\MissionController::show/$1');
$routes->post('admin/missions/(:num)/assignments', 'Admin\MissionController::storeAssignment/$1');
$routes->post('admin/missions/(:num)/assignments/(:num)', 'Admin\MissionController::updateAssignment/$1/$2');
$routes->delete('admin/missions/(:num)/assignments/(:num)', 'Admin\MissionController::removeAssignment/$1/$2');
```

### 5. View Template
**File:** `app/Views/admin/missions/show.php`

```php
<?= $this->extend('admin/layouts/admin_template') ?>

<?= $this->section('title') ?>
<?= esc($title) ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Mission Details Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard') ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('admin/missions') ?>">Missions</a></li>
                        <li class="breadcrumb-item active"><?= esc($mission['name']) ?></li>
                    </ol>
                </div>
                <h4 class="page-title"><?= esc($title) ?></h4>
            </div>
        </div>
    </div>

    <!-- Mission Information Card -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Mission Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Mission Code:</strong> <?= esc($mission['code']) ?></p>
                            <p><strong>Description:</strong> <?= esc($mission['description']) ?></p>
                            <p><strong>Start Date:</strong> <?= date('M d, Y', strtotime($mission['start_date'])) ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>End Date:</strong> <?= date('M d, Y', strtotime($mission['end_date'])) ?></p>
                            <p><strong>Total Budget:</strong> $<?= number_format($mission['total_budget'], 2) ?></p>
                            <p><strong>Status:</strong> <span class="badge badge-<?= $mission['status'] == 'active' ? 'success' : 'secondary' ?>"><?= ucfirst($mission['status']) ?></span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Assignments Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title mb-0">User Assignments</h5>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#assignUserModal">
                                <i class="mdi mdi-plus"></i> Assign User
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="close" data-dismiss="alert">
                                <span>&times;</span>
                            </button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="close" data-dismiss="alert">
                                <span>&times;</span>
                            </button>
                        </div>
                    <?php endif; ?>

                    <?php if (empty($assignments)): ?>
                        <div class="text-center py-4">
                            <p class="text-muted">No users assigned to this mission yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Buyer</th>
                                        <th>Commodity</th>
                                        <th>Budget</th>
                                        <th>Actual Amount</th>
                                        <th>Utilization</th>
                                        <th>Remarks</th>
                                        <th>Assigned Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($assignments as $assignment): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?= esc($assignment['first_name'] . ' ' . $assignment['last_name']) ?></strong>
                                                    <br><small class="text-muted"><?= esc($assignment['email']) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= esc($assignment['commodity_name']) ?></strong>
                                                    <br><small class="text-muted"><?= esc($assignment['commodity_code']) ?></small>
                                                </div>
                                            </td>
                                            <td>$<?= number_format($assignment['budget'], 2) ?></td>
                                            <td>$<?= number_format($assignment['actual_amount'], 2) ?></td>
                                            <td>
                                                <?php 
                                                $utilization = $assignment['budget'] > 0 ? ($assignment['actual_amount'] / $assignment['budget']) * 100 : 0;
                                                $progressClass = $utilization <= 50 ? 'success' : ($utilization <= 80 ? 'warning' : 'danger');
                                                ?>
                                                <div class="progress" style="height: 6px;">
                                                    <div class="progress-bar bg-<?= $progressClass ?>" 
                                                         style="width: <?= min($utilization, 100) ?>%"></div>
                                                </div>
                                                <small class="text-muted"><?= number_format($utilization, 1) ?>%</small>
                                            </td>
                                            <td><?= esc($assignment['remarks']) ?: '<span class="text-muted">-</span>' ?></td>
                                            <td><?= date('M d, Y', strtotime($assignment['created_at'])) ?></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-primary" 
                                                            onclick="editAssignment(<?= $assignment['id'] ?>)">
                                                        <i class="mdi mdi-pencil"></i>
                                                    </button>
                                                    <form method="post" 
                                                          action="<?= base_url("admin/missions/{$mission['id']}/assignments/{$assignment['id']}") ?>" 
                                                          class="d-inline"
                                                          onsubmit="return confirm('Are you sure you want to remove this assignment?')">
                                                        <input type="hidden" name="_method" value="DELETE">
                                                        <?= csrf_field() ?>
                                                        <button type="submit" class="btn btn-outline-danger">
                                                            <i class="mdi mdi-delete"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Assign User Modal -->
<div class="modal fade" id="assignUserModal" tabindex="-1" role="dialog" aria-labelledby="assignUserModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form method="post" action="<?= base_url("admin/missions/{$mission['id']}/assignments") ?>">
                <?= csrf_field() ?>
                <div class="modal-header">
                    <h5 class="modal-title" id="assignUserModalLabel">Assign User to Mission</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="user_id">Select Buyer <span class="text-danger">*</span></label>
                        <select class="form-control <?= isset($validation) && $validation->hasError('user_id') ? 'is-invalid' : '' ?>" 
                                id="user_id" name="user_id" required>
                            <option value="">Choose a buyer...</option>
                            <?php foreach ($buyers as $buyer): ?>
                                <option value="<?= $buyer['id'] ?>" <?= old('user_id') == $buyer['id'] ? 'selected' : '' ?>>
                                    <?= esc($buyer['first_name'] . ' ' . $buyer['last_name']) ?> - <?= esc($buyer['email']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (isset($validation) && $validation->hasError('user_id')): ?>
                            <div class="invalid-feedback">
                                <?= $validation->getError('user_id') ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="form-group">
                        <label for="commodity_id">Select Commodity <span class="text-danger">*</span></label>
                        <select class="form-control <?= isset($validation) && $validation->hasError('commodity_id') ? 'is-invalid' : '' ?>" 
                                id="commodity_id" name="commodity_id" required>
                            <option value="">Choose a commodity...</option>
                            <?php foreach ($commodities as $commodity): ?>
                                <option value="<?= $commodity['id'] ?>" <?= old('commodity_id') == $commodity['id'] ? 'selected' : '' ?>>
                                    <?= esc($commodity['name']) ?> (<?= esc($commodity['code']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (isset($validation) && $validation->hasError('commodity_id')): ?>
                            <div class="invalid-feedback">
                                <?= $validation->getError('commodity_id') ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="form-group">
                        <label for="budget">Budget Amount <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="number" 
                                   class="form-control <?= isset($validation) && $validation->hasError('budget') ? 'is-invalid' : '' ?>" 
                                   id="budget" 
                                   name="budget" 
                                   step="0.01" 
                                   min="0.01" 
                                   value="<?= old('budget') ?>" 
                                   placeholder="0.00" 
                                   required>
                            <?php if (isset($validation) && $validation->hasError('budget')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('budget') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="actual_amount">Actual Amount</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="number" 
                                   class="form-control" 
                                   id="actual_amount" 
                                   name="actual_amount" 
                                   step="0.01" 
                                   min="0" 
                                   value="<?= old('actual_amount', '0.00') ?>" 
                                   placeholder="0.00">
                        </div>
                        <small class="form-text text-muted">This will be automatically updated when transactions are made.</small>
                    </div>

                    <div class="form-group">
                        <label for="remarks">Remarks</label>
                        <textarea class="form-control" 
                                  id="remarks" 
                                  name="remarks" 
                                  rows="3" 
                                  placeholder="Optional remarks or notes..."><?= old('remarks') ?></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Assign User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editAssignment(assignmentId) {
    // TODO: Implement edit functionality in future iteration
    alert('Edit functionality will be implemented in the next iteration');
}

// Auto-show modal if there are validation errors
<?php if (isset($validation) && $validation->hasErrors() && old('user_id')): ?>
$(document).ready(function() {
    $('#assignUserModal').modal('show');
});
<?php endif; ?>
</script>

<?= $this->endSection() ?>
```

## Definition of Done

### Technical Checklist
- [ ] Migration created and executed successfully
- [ ] Model with proper validation rules implemented
- [ ] Controller methods follow RESTful conventions
- [ ] Routes configured correctly
- [ ] View template uses admin template system
- [ ] Form validation working correctly
- [ ] Success/error messages displayed properly
- [ ] Database constraints prevent duplicate assignments
- [ ] Code follows CodeIgniter 4 best practices
- [ ] No AJAX used (standard form submission)

### Functional Checklist
- [ ] Modal opens when "Assign User" button is clicked
- [ ] Buyer dropdown populates with active buyers only
- [ ] Commodity dropdown populates with active commodities
- [ ] Form validation prevents invalid submissions
- [ ] Duplicate assignment prevention works
- [ ] Success message shows after successful assignment
- [ ] Assignments list displays correctly
- [ ] Budget utilization calculation works
- [ ] Remove assignment functionality works

## Additional Implementation Files

### 6. CSS Styles (Optional Enhancement)
**File:** `public/assets/admin/css/missions.css`

```css
/* Mission Assignment Styles */
.assignment-card {
    transition: all 0.3s ease;
}

.assignment-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.progress-sm {
    height: 6px;
    border-radius: 3px;
}

.badge-assignment {
    font-size: 0.75rem;
    padding: 0.25em 0.6em;
}

.assignment-meta {
    font-size: 0.875rem;
    color: #6c757d;
}

.modal-header {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.modal-header .close {
    color: white;
    opacity: 0.8;
}

.modal-header .close:hover {
    opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group-sm > .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}
```

### 7. JavaScript Enhancements
**File:** `public/assets/admin/js/missions.js`

```javascript
/**
 * Mission Assignment Management
 */
class MissionAssignment {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeTooltips();
    }

    bindEvents() {
        // Clear form when modal is closed
        $('#assignUserModal').on('hidden.bs.modal', function () {
            $(this).find('form')[0].reset();
            $(this).find('.is-invalid').removeClass('is-invalid');
            $(this).find('.invalid-feedback').hide();
        });

        // Auto-calculate budget utilization on input change
        $(document).on('input', '#actual_amount', function() {
            const actualAmount = parseFloat($(this).val()) || 0;
            const budgetAmount = parseFloat($('#budget').val()) || 0;
            
            if (budgetAmount > 0) {
                const utilization = (actualAmount / budgetAmount) * 100;
                $('#utilization-preview').text(utilization.toFixed(1) + '%');
            }
        });

        // Confirm deletion
        $('.delete-assignment').on('click', function(e) {
            if (!confirm('Are you sure you want to remove this assignment? This action cannot be undone.')) {
                e.preventDefault();
                return false;
            }
        });

        // Form validation enhancement
        $('form[action*="assignments"]').on('submit', function() {
            const form = $(this);
            const submitBtn = form.find('button[type="submit"]');
            
            // Disable submit button to prevent double submission
            submitBtn.prop('disabled', true);
            submitBtn.html('<i class="mdi mdi-loading mdi-spin"></i> Processing...');
            
            // Re-enable after 3 seconds as fallback
            setTimeout(function() {
                submitBtn.prop('disabled', false);
                submitBtn.html('Assign User');
            }, 3000);
        });
    }

    initializeTooltips() {
        // Initialize tooltips for action buttons
        $('[data-toggle="tooltip"]').tooltip();
    }

    // Utility function to format currency
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    }

    // Validate form before submission
    validateAssignmentForm() {
        let isValid = true;
        const form = $('#assignUserModal form');
        
        // Reset previous validation states
        form.find('.is-invalid').removeClass('is-invalid');
        
        // Validate buyer selection
        const userId = form.find('#user_id').val();
        if (!userId) {
            form.find('#user_id').addClass('is-invalid');
            isValid = false;
        }
        
        // Validate commodity selection
        const commodityId = form.find('#commodity_id').val();
        if (!commodityId) {
            form.find('#commodity_id').addClass('is-invalid');
            isValid = false;
        }
        
        // Validate budget amount
        const budget = parseFloat(form.find('#budget').val());
        if (!budget || budget <= 0) {
            form.find('#budget').addClass('is-invalid');
            isValid = false;
        }
        
        return isValid;
    }
}

// Initialize on document ready
$(document).ready(function() {
    new MissionAssignment();
});
```

### 8. Helper Functions
**File:** `app/Helpers/mission_helper.php`

```php
<?php

/**
 * Mission Helper Functions
 */

if (!function_exists('calculate_utilization_percentage')) {
    /**
     * Calculate budget utilization percentage
     */
    function calculate_utilization_percentage($actualAmount, $budgetAmount)
    {
        if ($budgetAmount <= 0) {
            return 0;
        }
        
        return round(($actualAmount / $budgetAmount) * 100, 2);
    }
}

if (!function_exists('get_utilization_class')) {
    /**
     * Get CSS class based on utilization percentage
     */
    function get_utilization_class($utilizationPercentage)
    {
        if ($utilizationPercentage <= 50) {
            return 'success';
        } elseif ($utilizationPercentage <= 80) {
            return 'warning';
        } else {
            return 'danger';
        }
    }
}

if (!function_exists('format_mission_status')) {
    /**
     * Format mission status with appropriate badge
     */
    function format_mission_status($status)
    {
        $statusConfig = [
            'planning' => ['class' => 'secondary', 'text' => 'Planning'],
            'active' => ['class' => 'success', 'text' => 'Active'],
            'completed' => ['class' => 'primary', 'text' => 'Completed'],
            'cancelled' => ['class' => 'danger', 'text' => 'Cancelled']
        ];
        
        $config = $statusConfig[$status] ?? ['class' => 'secondary', 'text' => ucfirst($status)];
        
        return '<span class="badge badge-' . $config['class'] . '">' . $config['text'] . '</span>';
    }
}

if (!function_exists('generate_assignment_summary')) {
    /**
     * Generate assignment summary for reporting
     */
    function generate_assignment_summary($assignments)
    {
        $summary = [
            'total_assignments' => count($assignments),
            'total_budget' => 0,
            'total_actual' => 0,
            'commodities' => [],
            'buyers' => []
        ];
        
        foreach ($assignments as $assignment) {
            $summary['total_budget'] += $assignment['budget'];
            $summary['total_actual'] += $assignment['actual_amount'];
            
            // Track unique commodities
            if (!in_array($assignment['commodity_name'], $summary['commodities'])) {
                $summary['commodities'][] = $assignment['commodity_name'];
            }
            
            // Track unique buyers
            $buyerName = $assignment['first_name'] . ' ' . $assignment['last_name'];
            if (!in_array($buyerName, $summary['buyers'])) {
                $summary['buyers'][] = $buyerName;
            }
        }
        
        $summary['utilization_percentage'] = calculate_utilization_percentage(
            $summary['total_actual'], 
            $summary['total_budget']
        );
        
        return $summary;
    }
}
```

### 9. Configuration Updates
**File:** `app/Config/Validation.php` (Add custom rules)

```php
<?php

namespace App\Config;

use CodeIgniter\Config\BaseConfig;

class Validation extends BaseConfig
{
    // ... existing configuration

    /**
     * Custom validation rules for mission assignments
     */
    public $assignment_rules = [
        'user_id' => [
            'rules' => 'required|is_natural_no_zero|validate_active_buyer',
            'errors' => [
                'required' => 'Please select a buyer',
                'is_natural_no_zero' => 'Invalid buyer selection',
                'validate_active_buyer' => 'Selected buyer is not active'
            ]
        ],
        'commodity_id' => [
            'rules' => 'required|is_natural_no_zero|validate_active_commodity',
            'errors' => [
                'required' => 'Please select a commodity',
                'is_natural_no_zero' => 'Invalid commodity selection',
                'validate_active_commodity' => 'Selected commodity is not active'
            ]
        ],
        'budget' => [
            'rules' => 'required|decimal|greater_than[0]|validate_budget_limit',
            'errors' => [
                'required' => 'Budget amount is required',
                'decimal' => 'Budget must be a valid decimal number',
                'greater_than' => 'Budget must be greater than 0',
                'validate_budget_limit' => 'Budget exceeds mission total budget'
            ]
        ]
    ];
}
```

### 10. Custom Validation Rules
**File:** `app/Validation/AssignmentRules.php`

```php
<?php

namespace App\Validation;

use App\Models\UserModel;
use App\Models\CommodityModel;
use App\Models\MissionModel;

class AssignmentRules
{
    /**
     * Validate that the selected buyer is active
     */
    public function validate_active_buyer(string $value, string $params, array $data): bool
    {
        $userModel = new UserModel();
        $user = $userModel->find($value);
        
        return $user && $user['status'] === 'active' && $user['role'] === 'buyer';
    }

    /**
     * Validate that the selected commodity is active
     */
    public function validate_active_commodity(string $value, string $params, array $data): bool
    {
        $commodityModel = new CommodityModel();
        $commodity = $commodityModel->find($value);
        
        return $commodity && $commodity['status'] === 'active';
    }

    /**
     * Validate budget doesn't exceed mission total budget
     */
    public function validate_budget_limit(string $value, string $params, array $data): bool
    {
        if (!isset($data['mission_id'])) {
            return true; // Skip if mission_id not provided
        }
        
        $missionModel = new MissionModel();
        $mission = $missionModel->find($data['mission_id']);
        
        if (!$mission) {
            return false;
        }
        
        // Allow reasonable budget allocation (you can adjust this logic)
        return floatval($value) <= floatval($mission['total_budget']);
    }
}
```

### 11. Testing Considerations

#### Unit Tests
**File:** `tests/unit/Models/UserCommodityMissionModelTest.php`

```php
<?php

namespace Tests\Unit\Models;

use CodeIgniter\Test\CIUnitTestCase;
use CodeIgniter\Test\DatabaseTestTrait;
use App\Models\UserCommodityMissionModel;

class UserCommodityMissionModelTest extends CIUnitTestCase
{
    use DatabaseTestTrait;

    protected $model;
    protected $refresh = true;

    protected function setUp(): void
    {
        parent::setUp();
        $this->model = new UserCommodityMissionModel();
    }

    public function testCanCreateAssignment()
    {
        $data = [
            'user_id' => 1,
            'mission_id' => 1,
            'commodity_id' => 1,
            'budget' => 1000.00,
            'actual_amount' => 0.00,
            'remarks' => 'Test assignment'
        ];

        $result = $this->model->insert($data);
        $this->assertIsNumeric($result);
    }

    public function testPreventsDuplicateAssignments()
    {
        // Create first assignment
        $data = [
            'user_id' => 1,
            'mission_id' => 1,
            'commodity_id' => 1,
            'budget' => 1000.00
        ];

        $this->model->insert($data);

        // Try to create duplicate
        $duplicate = $this->model->insert($data);
        $this->assertFalse($duplicate);
    }

    public function testValidatesRequiredFields()
    {
        $data = [
            'user_id' => 1,
            // Missing required fields
        ];

        $result = $this->model->insert($data);
        $this->assertFalse($result);
        $this->assertNotEmpty($this->model->errors());
    }
}
```

## Implementation Timeline

### Sprint Planning (2-week sprint)

#### Week 1: Foundation
**Days 1-3:** Database & Model Setup
- Create and run migration
- Implement UserCommodityMissionModel
- Write unit tests for model
- Set up validation rules

**Days 4-5:** Controller Development
- Implement MissionController methods
- Add routing configuration
- Test controller endpoints

#### Week 2: Frontend & Integration
**Days 6-8:** View Development
- Create mission show template
- Implement assignment modal
- Add CSS styling and JavaScript

**Days 9-10:** Testing & Refinement
- Integration testing
- Bug fixes and refinements
- Code review and optimization

## Risk Assessment & Mitigation

### Technical Risks
1. **Database Constraints**: Unique key conflicts
   - *Mitigation*: Proper validation before insert/update operations

2. **Form Validation**: Client-side vs server-side validation mismatch
   - *Mitigation*: Comprehensive server-side validation with user-friendly error messages

3. **Data Integrity**: Orphaned records if related entities are deleted
   - *Mitigation*: Foreign key constraints with CASCADE options

### Business Risks
1. **Budget Overallocation**: Users assigning more budget than available
   - *Mitigation*: Budget validation rules and mission total budget checks

2. **Permission Issues**: Unauthorized access to assignment features
   - *Mitigation*: Proper authentication and role-based authorization

## Success Metrics

### Technical Metrics
- Zero critical bugs in production
- Page load time < 2 seconds
- Form submission success rate > 99%
- Database query performance < 100ms

### Business Metrics
- User adoption rate of assignment feature
- Reduction in manual assignment errors
- Improved budget tracking accuracy
- User satisfaction scores from admin users

This comprehensive implementation provides a robust, scalable solution for assigning users to missions while maintaining data integrity and providing an excellent user experience.