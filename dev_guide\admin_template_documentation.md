# DCBuyer Admin Template

A modern, responsive admin template for the DCBuyer platform built with Bootstrap 5 and CodeIgniter 4. This template maintains the same visual identity and branding as the main DCBuyer application.

## Features

- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **Modern UI**: Clean, professional interface following DCBuyer's design language
- **Sidebar Navigation**: Collapsible sidebar with nested menu support
- **Breadcrumb Navigation**: Easy navigation tracking
- **Flash Messages**: Built-in support for success, error, warning, and info messages
- **Data Tables**: Advanced table functionality with sorting, filtering, and pagination
- **Search Functionality**: Global search with real-time results
- **Notification System**: User notifications with badge counts
- **Loading States**: Interactive loading indicators for better UX
- **Theme Consistency**: Matches the DCBuyer brand colors and typography

## File Structure

```
app/
├── Views/
│   ├── templates/
│   │   └── admin_template.php     # Main admin template
│   └── admin/
│       ├── admin_dashboard.php    # Dashboard page example
│       └── products_list.php      # Products list page example
├── Controllers/
│   └── AdminController.php        # Admin controller example
public/
├── assets/
│   ├── css/
│   │   └── admin.css             # Admin-specific styles
│   ├── js/
│   │   └── admin.js              # Admin-specific JavaScript
│   └── images/
│       └── default-avatar.svg    # Default user avatar
```

## Installation

1. **Copy Template Files**: The template files have been created in your project:
   - `app/Views/templates/admin_template.php`
   - `public/assets/css/admin.css`
   - `public/assets/js/admin.js`

2. **Update Routes**: Admin routes have been added to `app/Config/Routes.php`

3. **Controller Setup**: Use the provided `AdminController.php` as a starting point

## Usage

### Basic Admin Page

Create a new admin page by extending the admin template:

```php
<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-12">
        <div class="admin-card">
            <div class="card-header">
                <h5 class="mb-0">Page Title</h5>
            </div>
            <div class="card-body">
                <!-- Your content here -->
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
```

### Controller Setup

In your controller, set up the required data:

```php
class YourAdminController extends BaseController
{
    protected $data = [];
    
    public function index()
    {
        $this->data['title'] = 'Page Title - DCBuyer Admin';
        $this->data['active_menu'] = 'your_menu_item';
        $this->data['user_name'] = session('user_name');
        $this->data['user_email'] = session('user_email');
        $this->data['user_role'] = session('user_role');
        
        // Breadcrumbs
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Your Page', 'url' => base_url('admin/your-page')]
        ];
        
        // Page header
        $this->data['page_title'] = 'Your Page Title';
        $this->data['page_description'] = 'Description of your page';
        
        return view('admin/your_page', $this->data);
    }
}
```

## Template Variables

### Required Variables

- `$title`: Page title for the HTML `<title>` tag
- `$active_menu`: Current active menu item identifier
- `$user_name`: Current user's name
- `$user_email`: Current user's email
- `$user_role`: Current user's role

### Optional Variables

- `$description`: Meta description
- `$breadcrumbs`: Array of breadcrumb items
- `$page_title`: Main page heading
- `$page_description`: Page description text
- `$page_actions`: HTML for page action buttons
- `$notification_count`: Number of unread notifications
- `$notifications`: Array of notification items
- `$pending_orders`: Number of pending orders

## Sidebar Menu

The sidebar menu is defined in the template and supports:

- **Single Items**: Direct navigation links
- **Dropdown Menus**: Nested navigation with collapse/expand
- **Badges**: Show counts (like pending orders)
- **Icons**: Font Awesome icons for visual hierarchy

### Menu Items

Current menu structure:
- Dashboard
- Products (with submenu: All Products, Categories, Inventory)
- Orders
- Users (with submenu: All Users, Farmers, Buyers)
- Transactions
- Analytics
- Settings (with submenu: General, System, Notifications)

### Active States

Set the `$active_menu` variable to highlight the current page:

```php
$this->data['active_menu'] = 'dashboard';     // For dashboard
$this->data['active_menu'] = 'products';     // For products section
$this->data['active_menu'] = 'users';        // For users section
```

## Styling

### Color Variables

The template uses CSS custom properties for consistent theming:

```css
:root {
    --admin-primary: #2E7D32;      /* DCBuyer green */
    --admin-secondary: #4CAF50;    /* Light green */
    --admin-dark: #1A365D;         /* Dark blue */
    --admin-light: #f8f9fa;        /* Light background */
}
```

### Custom Classes

- `.admin-card`: Standard card component
- `.btn-admin-primary`: Primary action button
- `.btn-admin-secondary`: Secondary action button
- `.admin-table`: Enhanced table styling
- `.stats-card`: Dashboard statistics card

## JavaScript Features

### Available Functions

The `AdminTemplate` object provides utility functions:

```javascript
// Show notifications
AdminTemplate.showNotification('Message', 'success');

// Confirm actions
AdminTemplate.confirmAction('Are you sure?', callback);

// Loading states
AdminTemplate.addLoadingState(button);
AdminTemplate.removeLoadingState(button);

// Data tables
AdminTemplate.initializeDataTable('tableId');
```

### Data Tables

Initialize enhanced table functionality:

```javascript
AdminTemplate.initializeDataTable('myTable', {
    sortable: true,
    searchable: true,
    pagination: true
});
```

## Responsive Features

- **Mobile Sidebar**: Overlay sidebar on mobile devices
- **Collapsible Navigation**: Desktop sidebar can be collapsed
- **Responsive Tables**: Horizontal scrolling on small screens
- **Touch-Friendly**: Optimized for touch interactions

## Browser Support

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## Customization

### Adding New Menu Items

Edit the sidebar navigation in `admin_template.php`:

```php
<a class="nav-link <?= $active_menu === 'new_item' ? 'active' : '' ?>" 
   href="<?= base_url('admin/new-item') ?>">
    <i class="fas fa-icon"></i>
    <span>New Item</span>
</a>
```

### Custom Styles

Add custom styles in the `styles` section:

```php
<?= $this->section('styles') ?>
<style>
    .custom-component {
        /* Your styles */
    }
</style>
<?= $this->endSection() ?>
```

### Custom JavaScript

Add custom scripts in the `scripts` section:

```php
<?= $this->section('scripts') ?>
<script>
    // Your JavaScript code
</script>
<?= $this->endSection() ?>
```

## Performance

- **Optimized Assets**: Minified CSS and JavaScript
- **CDN Resources**: Bootstrap and Font Awesome from CDN
- **Efficient DOM**: Minimal DOM manipulation
- **Lazy Loading**: Images and heavy components load on demand

## Security

- **CSRF Protection**: Built-in CodeIgniter CSRF protection
- **Input Sanitization**: All user inputs are escaped
- **Authentication**: Route filtering for admin access
- **XSS Prevention**: Output escaping throughout

## Contributing

When contributing to the admin template:

1. Follow the existing code style
2. Test on multiple screen sizes
3. Ensure accessibility compliance
4. Update documentation for new features

## Author

**Noland Gande**
- Website: www.dakoiims.com
- Email: <EMAIL>

## License

This admin template is part of the DCBuyer project. All rights reserved.