<?php

namespace App\Models;

use CodeIgniter\Model;

class MissionModel extends Model
{
    protected $table = 'mission';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false; // We'll handle soft deletes manually
    protected $protectFields = true;
    protected $allowedFields = [
        'mission_number',
        'mission_name',
        'mission_date',
        'mission_status',
        'user_id',
        'commodity_id',
        'budgeted_amount',
        'actual_amount',
        'remarks',
        'created_by',
        'updated_by',
        'deleted_by',
        'is_deleted'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'mission_number' => 'permit_empty|max_length[20]|is_unique[mission.mission_number,id,{id}]',
        'mission_name' => 'required|min_length[3]|max_length[255]',
        'mission_date' => 'required|valid_date',
        'mission_status' => 'required|in_list[pending,in_progress,completed,cancelled]',
        'user_id' => 'permit_empty|integer',
        'commodity_id' => 'permit_empty|integer',
        'budgeted_amount' => 'permit_empty|decimal',
        'actual_amount' => 'permit_empty|decimal',
        'remarks' => 'permit_empty|max_length[1000]'
    ];

    protected $validationMessages = [
        'mission_number' => [
            'max_length' => 'Mission number cannot exceed 20 characters',
            'is_unique' => 'This mission number already exists'
        ],
        'mission_name' => [
            'required' => 'Mission name is required',
            'min_length' => 'Mission name must be at least 3 characters long',
            'max_length' => 'Mission name cannot exceed 255 characters'
        ],
        'mission_date' => [
            'required' => 'Mission date is required',
            'valid_date' => 'Please provide a valid date'
        ],
        'mission_status' => [
            'required' => 'Mission status is required',
            'in_list' => 'Mission status must be one of: pending, in_progress, completed, cancelled'
        ],
        'user_id' => [
            'integer' => 'Invalid user selected'
        ],
        'commodity_id' => [
            'integer' => 'Invalid commodity selected'
        ],
        'budgeted_amount' => [
            'decimal' => 'Budgeted amount must be a valid decimal number'
        ],
        'actual_amount' => [
            'decimal' => 'Actual amount must be a valid decimal number'
        ],
        'remarks' => [
            'max_length' => 'Remarks cannot exceed 1000 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['setCreatedBy', 'generateMissionNumber'];
    protected $beforeUpdate = ['setUpdatedBy'];

    /**
     * Set created_by field before insert
     */
    protected function setCreatedBy(array $data)
    {
        if (!isset($data['data']['created_by']) && session()->has('user_id')) {
            $data['data']['created_by'] = session()->get('user_id');
        }
        return $data;
    }

    /**
     * Set updated_by field before update
     */
    protected function setUpdatedBy(array $data)
    {
        if (!isset($data['data']['updated_by']) && session()->has('user_id')) {
            $data['data']['updated_by'] = session()->get('user_id');
        }
        return $data;
    }

    /**
     * Generate mission number before insert
     */
    protected function generateMissionNumber(array $data)
    {
        if (!isset($data['data']['mission_number']) || empty($data['data']['mission_number'])) {
            $data['data']['mission_number'] = $this->getNextMissionNumber();
        }
        return $data;
    }

    /**
     * Generate the next mission number in format: {increment}{year}
     * Examples: 12025, 22025, 1022025
     */
    public function getNextMissionNumber(): string
    {
        $currentYear = date('Y');

        // Find the highest mission number for the current year
        $lastMission = $this->select('mission_number')
                           ->where('mission_number LIKE', '%' . $currentYear)
                           ->where('is_deleted', false)
                           ->orderBy('mission_number', 'DESC')
                           ->first();

        if ($lastMission) {
            // Extract the increment part (everything except the last 4 digits which are the year)
            $missionNumber = $lastMission['mission_number'];
            $yearLength = strlen($currentYear);
            $incrementPart = substr($missionNumber, 0, -$yearLength);
            $nextIncrement = intval($incrementPart) + 1;
        } else {
            // First mission of the year
            $nextIncrement = 1;
        }

        return $nextIncrement . $currentYear;
    }

    /**
     * Get all active missions (not soft deleted)
     */
    public function getActiveMissions(): array
    {
        return $this->where('is_deleted', false)
                   ->orderBy('mission_date', 'DESC')
                   ->findAll();
    }

    /**
     * Get mission by ID (only if not deleted)
     */
    public function getActiveMission(int $id): ?array
    {
        $mission = $this->where('id', $id)
                       ->where('is_deleted', false)
                       ->first();
        
        return $mission ?: null;
    }

    /**
     * Soft delete a mission
     */
    public function softDelete(int $id, ?int $deletedBy = null): bool
    {
        $data = [
            'is_deleted' => true,
            'deleted_at' => date('Y-m-d H:i:s'),
            'deleted_by' => $deletedBy ?: session()->get('user_id')
        ];
        
        return $this->update($id, $data);
    }

    /**
     * Get missions with pagination and search
     */
    public function getMissionsPaginated(int $perPage = 20, string $search = '', string $status = ''): array
    {
        $query = $this->where('is_deleted', false);

        if (!empty($search)) {
            $query->groupStart()
                  ->like('mission_name', $search)
                  ->orLike('mission_number', $search)
                  ->groupEnd();
        }

        if (!empty($status)) {
            $query->where('mission_status', $status);
        }

        return $query->orderBy('mission_date', 'DESC')
                    ->paginate($perPage);
    }

    /**
     * Get mission statistics
     */
    public function getMissionStats(): array
    {
        $total = $this->where('is_deleted', false)->countAllResults();
        
        $statusStats = $this->select('mission_status, COUNT(*) as count')
                           ->where('is_deleted', false)
                           ->groupBy('mission_status')
                           ->orderBy('count', 'DESC')
                           ->findAll();

        $recentMissions = $this->where('is_deleted', false)
                              ->where('mission_date >=', date('Y-m-d', strtotime('-30 days')))
                              ->countAllResults();

        return [
            'total_missions' => $total,
            'status_distribution' => $statusStats,
            'recent_missions' => $recentMissions
        ];
    }

    /**
     * Check if mission name exists (for validation)
     */
    public function missionNameExists(string $name, ?int $excludeId = null): bool
    {
        $query = $this->where('LOWER(mission_name)', strtolower($name))
                     ->where('is_deleted', false);
        
        if ($excludeId) {
            $query->where('id !=', $excludeId);
        }
        
        return $query->countAllResults() > 0;
    }

    /**
     * Get missions by status
     */
    public function getMissionsByStatus(string $status): array
    {
        return $this->where('mission_status', $status)
                   ->where('is_deleted', false)
                   ->orderBy('mission_date', 'DESC')
                   ->findAll();
    }

    /**
     * Get missions by date range
     */
    public function getMissionsByDateRange(string $startDate, string $endDate): array
    {
        return $this->where('mission_date >=', $startDate)
                   ->where('mission_date <=', $endDate)
                   ->where('is_deleted', false)
                   ->orderBy('mission_date', 'DESC')
                   ->findAll();
    }

    /**
     * Restore deleted mission
     */
    public function restoreMission(int $id, ?int $restoredBy = null): bool
    {
        $data = [
            'is_deleted' => false,
            'deleted_at' => null,
            'deleted_by' => null
        ];
        
        if ($restoredBy) {
            $data['updated_by'] = $restoredBy;
        }
        
        return $this->update($id, $data);
    }

    /**
     * Get upcoming missions (future dates)
     */
    public function getUpcomingMissions(int $limit = 10): array
    {
        return $this->where('mission_date >=', date('Y-m-d'))
                   ->where('is_deleted', false)
                   ->orderBy('mission_date', 'ASC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Get overdue missions (past dates with pending/in_progress status)
     */
    public function getOverdueMissions(): array
    {
        return $this->where('mission_date <', date('Y-m-d'))
                   ->whereIn('mission_status', ['pending', 'in_progress'])
                   ->where('is_deleted', false)
                   ->orderBy('mission_date', 'ASC')
                   ->findAll();
    }

    /**
     * Get mission with user and commodity details
     */
    public function getMissionWithDetails(int $id): ?array
    {
        $mission = $this->select('mission.*, users.fullname as user_name, users.username, commodities.commodity_name, commodities.unit_of_measurement')
                       ->join('users', 'users.id = mission.user_id', 'left')
                       ->join('commodities', 'commodities.commodity_id = mission.commodity_id', 'left')
                       ->where('mission.id', $id)
                       ->where('mission.is_deleted', false)
                       ->first();

        return $mission ?: null;
    }

    /**
     * Get missions with user and commodity details (with pagination)
     */
    public function getMissionsWithDetailsPaginated(int $perPage = 20, string $search = '', string $status = ''): array
    {
        $query = $this->select('mission.*, users.fullname as user_name, users.username, commodities.commodity_name, commodities.unit_of_measurement')
                     ->join('users', 'users.id = mission.user_id', 'left')
                     ->join('commodities', 'commodities.commodity_id = mission.commodity_id', 'left')
                     ->where('mission.is_deleted', false);

        if (!empty($search)) {
            $query->groupStart()
                  ->like('mission.mission_name', $search)
                  ->orLike('mission.mission_number', $search)
                  ->orLike('users.fullname', $search)
                  ->orLike('commodities.commodity_name', $search)
                  ->groupEnd();
        }

        if (!empty($status)) {
            $query->where('mission.mission_status', $status);
        }

        return $query->orderBy('mission.mission_date', 'DESC')
                    ->paginate($perPage);
    }

    /**
     * Get missions assigned to a specific user
     */
    public function getMissionsByUser(int $userId): array
    {
        return $this->select('mission.*, commodities.commodity_name, commodities.unit_of_measurement')
                   ->join('commodities', 'commodities.commodity_id = mission.commodity_id', 'left')
                   ->where('mission.user_id', $userId)
                   ->where('mission.is_deleted', false)
                   ->orderBy('mission.mission_date', 'DESC')
                   ->findAll();
    }

    /**
     * Get missions for a specific commodity
     */
    public function getMissionsByCommodity(int $commodityId): array
    {
        return $this->select('mission.*, users.fullname as user_name, users.username')
                   ->join('users', 'users.id = mission.user_id', 'left')
                   ->where('mission.commodity_id', $commodityId)
                   ->where('mission.is_deleted', false)
                   ->orderBy('mission.mission_date', 'DESC')
                   ->findAll();
    }
}
