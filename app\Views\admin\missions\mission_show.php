<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .detail-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
    }

    .detail-header {
        border-bottom: 2px solid #f8f9fa;
        padding-bottom: 1.5rem;
        margin-bottom: 2rem;
    }

    .detail-title {
        font-size: 1.75rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .detail-subtitle {
        color: #6c757d;
        font-size: 1rem;
    }

    .detail-row {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .detail-row:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }

    .detail-label {
        flex: 0 0 200px;
        font-weight: 600;
        color: #495057;
        display: flex;
        align-items: center;
    }

    .detail-value {
        flex: 1;
        color: #2c3e50;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-size: 0.875rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: inline-flex;
        align-items: center;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .status-in_progress {
        background-color: #cce5ff;
        color: #004085;
        border: 1px solid #74c0fc;
    }

    .status-completed {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #51cf66;
    }

    .status-cancelled {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #ff6b6b;
    }

    .status-badge i {
        margin-right: 0.5rem;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-top: 1rem;
    }

    .info-item {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        border-left: 4px solid var(--bs-primary);
    }

    .info-item h6 {
        color: #495057;
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .info-item .value {
        color: #2c3e50;
        font-size: 1.1rem;
        font-weight: 500;
    }

    .remarks-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        margin-top: 1rem;
    }

    .remarks-section h6 {
        color: #495057;
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .remarks-content {
        color: #2c3e50;
        line-height: 1.6;
        white-space: pre-wrap;
    }

    .audit-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }

    .audit-info small {
        color: #6c757d;
        display: block;
        margin-bottom: 0.25rem;
    }

    @media (max-width: 768px) {
        .detail-row {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .detail-label {
            flex: none;
            margin-bottom: 0.5rem;
        }
        
        .detail-value {
            flex: none;
            width: 100%;
        }
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Main Details Card -->
    <div class="detail-card">
        <div class="detail-header">
            <div class="detail-title">
                <i class="fas fa-tasks me-2"></i>
                <?= esc($mission['mission_name']) ?>
            </div>
            <div class="detail-subtitle">
                Mission Number: <?= esc($mission['mission_number']) ?> | ID: #<?= $mission['id'] ?>
            </div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">
                <i class="fas fa-hashtag me-2 text-primary"></i>
                Mission Number
            </div>
            <div class="detail-value">
                <strong><?= esc($mission['mission_number']) ?></strong>
            </div>
        </div>

        <div class="detail-row">
            <div class="detail-label">
                <i class="fas fa-tag me-2 text-primary"></i>
                Mission Name
            </div>
            <div class="detail-value">
                <strong><?= esc($mission['mission_name']) ?></strong>
            </div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">
                <i class="fas fa-calendar me-2 text-primary"></i>
                Mission Date
            </div>
            <div class="detail-value">
                <strong><?= date('F j, Y', strtotime($mission['mission_date'])) ?></strong>
                <small class="text-muted ms-2">(<?= date('l', strtotime($mission['mission_date'])) ?>)</small>
            </div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">
                <i class="fas fa-info-circle me-2 text-primary"></i>
                Status
            </div>
            <div class="detail-value">
                <span class="status-badge status-<?= $mission['mission_status'] ?>">
                    <i class="fas fa-<?= $mission['mission_status'] === 'completed' ? 'check-circle' : ($mission['mission_status'] === 'in_progress' ? 'clock' : ($mission['mission_status'] === 'cancelled' ? 'times-circle' : 'hourglass-half')) ?>"></i>
                    <?= ucfirst(str_replace('_', ' ', $mission['mission_status'])) ?>
                </span>
            </div>
        </div>
        
        <?php if (!empty($mission['remarks'])): ?>
        <div class="remarks-section">
            <h6>
                <i class="fas fa-comment-alt me-2"></i>
                Remarks
            </h6>
            <div class="remarks-content">
                <?= esc($mission['remarks']) ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Additional Information -->
    <div class="detail-card">
        <h5 class="mb-3">
            <i class="fas fa-info-circle me-2 text-primary"></i>
            Additional Information
        </h5>
        
        <div class="info-grid">
            <div class="info-item">
                <h6>Created Date</h6>
                <div class="value">
                    <?= date('F j, Y \a\t g:i A', strtotime($mission['created_at'])) ?>
                </div>
            </div>
            
            <?php if ($mission['updated_at'] && $mission['updated_at'] !== $mission['created_at']): ?>
            <div class="info-item">
                <h6>Last Updated</h6>
                <div class="value">
                    <?= date('F j, Y \a\t g:i A', strtotime($mission['updated_at'])) ?>
                </div>
            </div>
            <?php endif; ?>
            
            <div class="info-item">
                <h6>Mission Number</h6>
                <div class="value">
                    <?= esc($mission['mission_number']) ?>
                </div>
            </div>
            
            <div class="info-item">
                <h6>Days Until Mission</h6>
                <div class="value">
                    <?php 
                    $today = new DateTime();
                    $missionDate = new DateTime($mission['mission_date']);
                    $diff = $today->diff($missionDate);
                    
                    if ($missionDate < $today) {
                        echo '<span class="text-danger">' . $diff->days . ' days ago</span>';
                    } elseif ($missionDate == $today) {
                        echo '<span class="text-warning">Today</span>';
                    } else {
                        echo '<span class="text-success">' . $diff->days . ' days remaining</span>';
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <div class="audit-info">
            <small><strong>Created:</strong> <?= date('M j, Y \a\t g:i A', strtotime($mission['created_at'])) ?></small>
            <?php if ($mission['updated_at'] && $mission['updated_at'] !== $mission['created_at']): ?>
            <small><strong>Last Updated:</strong> <?= date('M j, Y \a\t g:i A', strtotime($mission['updated_at'])) ?></small>
            <?php endif; ?>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="detail-card">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <span class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Mission details and specifications
                </span>
            </div>
            <div>
                <a href="<?= base_url('admin/missions') ?>" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left me-2"></i>Back to List
                </a>
                <a href="<?= base_url('admin/missions/' . $mission['id'] . '/edit') ?>" class="btn btn-admin-primary">
                    <i class="fas fa-edit me-2"></i>Edit Mission
                </a>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
