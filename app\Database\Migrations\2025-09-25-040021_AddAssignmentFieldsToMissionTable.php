<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddAssignmentFieldsToMissionTable extends Migration
{
    public function up()
    {
        $fields = [
            'user_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
                'after' => 'mission_status'
            ],
            'commodity_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
                'after' => 'user_id'
            ],
            'budgeted_amount' => [
                'type' => 'DECIMAL',
                'constraint' => '15,2',
                'null' => true,
                'after' => 'commodity_id'
            ],
            'actual_amount' => [
                'type' => 'DECIMAL',
                'constraint' => '15,2',
                'null' => true,
                'after' => 'budgeted_amount'
            ]
        ];

        $this->forge->addColumn('mission', $fields);

        // Add indexes
        $this->forge->addKey('user_id', false, false, 'mission');
        $this->forge->addKey('commodity_id', false, false, 'mission');

        // Add foreign key constraints (if supported by the database)
        if ($this->db->DBDriver === 'Postgre') {
            $this->db->query('ALTER TABLE mission ADD CONSTRAINT fk_mission_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL');
            $this->db->query('ALTER TABLE mission ADD CONSTRAINT fk_mission_commodity FOREIGN KEY (commodity_id) REFERENCES commodities(commodity_id) ON DELETE SET NULL');
        }
    }

    public function down()
    {
        // Drop foreign key constraints first (if they exist)
        if ($this->db->DBDriver === 'Postgre') {
            $this->db->query('ALTER TABLE mission DROP CONSTRAINT IF EXISTS fk_mission_user');
            $this->db->query('ALTER TABLE mission DROP CONSTRAINT IF EXISTS fk_mission_commodity');
        }

        // Drop the columns
        $this->forge->dropColumn('mission', ['user_id', 'commodity_id', 'budgeted_amount', 'actual_amount']);
    }
}
