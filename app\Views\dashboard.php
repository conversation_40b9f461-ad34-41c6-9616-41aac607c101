<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .welcome-card {
        background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(46, 125, 50, 0.3);
    }
    
    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        text-align: center;
        margin-bottom: 1rem;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: 1px solid #e9ecef;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }
    
    .stats-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        display: block;
    }
    
    .stats-icon.text-primary { color: #2E7D32 !important; }
    .stats-icon.text-success { color: #4CAF50 !important; }
    .stats-icon.text-warning { color: #FF9800 !important; }
    .stats-icon.text-info { color: #2196F3 !important; }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #1A365D;
        line-height: 1;
        margin-bottom: 0.5rem;
    }
    
    .stats-label {
        color: #666;
        font-weight: 500;
        font-size: 1rem;
    }
    
    .stats-trend {
        font-size: 0.85rem;
        margin-top: 0.5rem;
        font-weight: 500;
    }
    
    .trend-up { color: #4CAF50; }
    .trend-down { color: #f44336; }
    
    .action-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        height: 100%;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: 1px solid #e9ecef;
    }
    
    .action-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
    
    .action-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
    }
    
    .action-icon.text-success { color: #4CAF50; }
    .action-icon.text-primary { color: #2196F3; }
    .action-icon.text-warning { color: #FF9800; }
    
    .action-card .btn {
        margin-top: 1rem;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
    }
    
    .dashboard-section {
        margin-bottom: 2rem;
    }
    
    .section-title {
        color: #1A365D;
        font-weight: 600;
        margin-bottom: 1.5rem;
        font-size: 1.25rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Welcome Section -->
<div class="welcome-card dashboard-section">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="h3 mb-3">
                <i class="fas fa-tachometer-alt me-2"></i>Welcome to DCBuyer Dashboard
            </h1>
            <p class="mb-0 opacity-90">
                Hello, <?= esc($user_name ?? $user_email) ?>! Manage your commodity trading activities from this central hub.
                <?php if (isset($user_role)): ?>
                    <span class="badge bg-light text-dark ms-2"><?= ucfirst(esc($user_role)) ?></span>
                <?php endif; ?>
            </p>
        </div>
        <div class="col-md-4 text-md-end">
            <div class="text-white-50">
                <i class="fas fa-calendar-alt me-1"></i>
                <?= date('l, F j, Y') ?>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="dashboard-section">
    <h4 class="section-title">Overview Statistics</h4>
    <div class="row">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <i class="fas fa-shopping-cart stats-icon text-primary"></i>
                <div class="stats-number">24</div>
                <div class="stats-label">Active Orders</div>
                <div class="stats-trend trend-up">
                    <i class="fas fa-arrow-up me-1"></i>8% increase
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <i class="fas fa-seedling stats-icon text-success"></i>
                <div class="stats-number">156</div>
                <div class="stats-label">Products Listed</div>
                <div class="stats-trend trend-up">
                    <i class="fas fa-arrow-up me-1"></i>12% increase
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <i class="fas fa-users stats-icon text-warning"></i>
                <div class="stats-number">89</div>
                <div class="stats-label">Active Buyers</div>
                <div class="stats-trend trend-up">
                    <i class="fas fa-arrow-up me-1"></i>5% increase
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <i class="fas fa-dollar-sign stats-icon text-info"></i>
                <div class="stats-number">$12.5K</div>
                <div class="stats-label">Monthly Revenue</div>
                <div class="stats-trend trend-up">
                    <i class="fas fa-arrow-up me-1"></i>15% increase
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="dashboard-section">
    <h4 class="section-title">Quick Actions</h4>
    <div class="row">
        <div class="col-md-4 mb-3">
            <div class="action-card">
                <i class="fas fa-plus-circle action-icon text-success"></i>
                <h5 class="card-title">Add New Product</h5>
                <p class="card-text">List a new commodity for trading</p>
                <button class="btn btn-admin-primary">Add Product</button>
            </div>
        </div>
        
        <div class="col-md-4 mb-3">
            <div class="action-card">
                <i class="fas fa-chart-line action-icon text-primary"></i>
                <h5 class="card-title">View Analytics</h5>
                <p class="card-text">Check your trading performance</p>
                <button class="btn btn-admin-primary">View Reports</button>
            </div>
        </div>
        
        <div class="col-md-4 mb-3">
            <div class="action-card">
                <i class="fas fa-handshake action-icon text-warning"></i>
                <h5 class="card-title">Manage Orders</h5>
                <p class="card-text">Process pending transactions</p>
                <button class="btn btn-admin-primary">Manage Orders</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
